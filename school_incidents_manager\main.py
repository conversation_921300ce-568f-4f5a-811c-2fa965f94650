#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# إضافة مسار src إلى Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from ui.main_window import MainWindow
from utils.qt_fixes import setup_qt_environment, get_optimized_stylesheet

def main():
    """تشغيل التطبيق الرئيسي"""

    # إعداد بيئة Qt المحسنة (يجب أن يكون قبل إنشاء QApplication)
    setup_qt_environment()

    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)

    # إعداد اللغة العربية واتجاه النص
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد الخط العربي
    font = QFont("Arial Unicode MS", 12)
    app.setFont(font)
    
    # إعداد الستايل المحسن (بدون خصائص غير مدعومة)
    app.setStyleSheet(get_optimized_stylesheet())
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
