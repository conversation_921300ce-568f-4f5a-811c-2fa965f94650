#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# إضافة مسار src إلى Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from ui.main_window import MainWindow

def main():
    """تشغيل التطبيق الرئيسي"""
    
    # إنشاء تطبيق Qt
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية واتجاه النص
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد الخط العربي
    font = QFont("Arial Unicode MS", 12)
    app.setFont(font)
    
    # إعداد الستايل الحديث
    app.setStyleSheet("""
        QWidget {
            font-family: 'Arial Unicode MS', 'Tahoma', sans-serif;
            font-size: 12px;
            color: #2c3e50;
        }
        
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QLabel {
            color: #2c3e50;
        }
        
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #21618c;
        }
        
        QLineEdit, QTextEdit, QComboBox {
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
        }
        
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
            border-color: #3498db;
        }
        
        QTableWidget {
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        QTableWidget::item:selected {
            background-color: #3498db;
            color: white;
        }
        
        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
        }
        
        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            background-color: white;
        }
        
        QTabBar::tab {
            background-color: #ecf0f1;
            padding: 10px 20px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        
        QTabBar::tab:selected {
            background-color: #3498db;
            color: white;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 10px 0 10px;
            color: #2c3e50;
        }
        
        QScrollBar:vertical {
            border: none;
            background-color: #ecf0f1;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #bdc3c7;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #95a5a6;
        }
    """)
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
