from dataclasses import dataclass
from datetime import date
from typing import Optional

@dataclass
class AcademicYear:
    id: int
    year_name: str
    
@dataclass
class EducationalInstitution:
    id: int
    institution_code: str
    institution_name: str
    community_name: Optional[str]
    environment: str  # قروي، حضري
    level: str  # ابتدائي، إعدادي، تأهيلي
    institution_type: Optional[str]
    director_name: Optional[str]
    phone_number: Optional[str]
    email: Optional[str]
    academic_year_id: int

@dataclass
class Incident:
    id: int
    file_number: str
    victim_name: str
    student_path_number: Optional[str]
    gender: str  # ذكر، أنثى
    institution_id: int
    reference: Optional[str]
    incident_date: date
    settlement_date: date
    status: str  # في طور التسوية، تمت التسوية، مرفوض، تم الدفع
    incident_type: str  # حادثة مدرسية، حادثة تنقل، حادثة رياضية
    severity: Optional[str]  # عادية، خطيرة، وفاة
    academic_year_id: int

@dataclass
class Beneficiary:
    id: int
    incident_id: int
    is_father: bool
    is_mother: bool
    is_guardian: bool
    full_name: Optional[str]
    mobile_phone: Optional[str]

@dataclass
class User:
    id: int
    username: str
    password: str
    role: str  # admin، user

# ثوابت التطبيق
class Constants:
    GENDERS = ["ذكر", "أنثى"]
    ENVIRONMENTS = ["قروي", "حضري"]
    LEVELS = ["ابتدائي", "إعدادي", "تأهيلي"]
    STATUSES = ["في طور التسوية", "تمت التسوية", "مرفوض", "تم الدفع"]
    INCIDENT_TYPES = ["حادثة مدرسية", "حادثة تنقل", "حادثة رياضية"]
    SEVERITIES = ["عادية", "خطيرة", "وفاة"]
    
    # ألوان التطبيق
    PRIMARY_COLOR = "#2E86AB"
    SECONDARY_COLOR = "#A23B72"
    SUCCESS_COLOR = "#43AA8B"
    WARNING_COLOR = "#F9844A"
    DANGER_COLOR = "#F8333C"
    BACKGROUND_COLOR = "#F8F9FA"
    TEXT_COLOR = "#212529"
