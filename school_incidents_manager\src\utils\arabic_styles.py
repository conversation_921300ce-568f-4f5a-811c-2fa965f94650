#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
أنماط CSS محسنة للغة العربية
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

def get_arabic_font():
    """الحصول على خط عربي محسن"""
    # قائمة الخطوط العربية المفضلة بترتيب الأولوية
    arabic_fonts = [
        "Segoe UI",
        "Tahoma", 
        "Arial Unicode MS",
        "Microsoft Sans Serif",
        "Arial"
    ]
    
    for font_name in arabic_fonts:
        font = QFont(font_name, 11)
        font.setStyleHint(QFont.System)
        if font.family() == font_name:
            return font
    
    # إذا لم يتم العثور على أي خط، استخدم الخط الافتراضي
    return QFont("Arial", 11)

def get_enhanced_arabic_stylesheet():
    """الحصول على stylesheet محسن للعربية"""
    return """
    /* إعدادات عامة للخطوط العربية */
    * {
        font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', Arial, sans-serif;
        font-size: 11pt;
        color: #212529;
    }
    
    QMainWindow {
        background-color: #f8f9fa;
        color: #212529;
    }
    
    /* العناوين الرئيسية */
    QLabel[objectName="page_title"] {
        font-size: 24pt;
        font-weight: bold;
        color: #2E86AB;
        text-align: right;
        qproperty-alignment: AlignRight;
        margin-bottom: 20px;
    }
    
    /* العناوين الفرعية */
    QLabel[objectName="section_title"] {
        font-size: 18pt;
        font-weight: bold;
        color: #2E86AB;
        text-align: right;
        qproperty-alignment: AlignRight;
        margin-bottom: 15px;
    }
    
    /* النصوص العادية */
    QLabel {
        color: #212529;
        text-align: right;
        qproperty-alignment: AlignRight;
    }
    
    /* الشريط العلوي */
    #header_widget {
        background-color: #ffffff;
        border: none;
        padding: 10px;
        border-bottom: 2px solid #2E86AB;
    }
    
    #header_title {
        color: #2E86AB;
        font-size: 18pt;
        font-weight: bold;
        text-align: right;
        qproperty-alignment: AlignRight;
    }
    
    /* القائمة الجانبية */
    #sidebar {
        background-color: #ffffff;
        border-right: 1px solid #dee2e6;
        max-width: 250px;
        min-width: 200px;
    }
    
    #sidebar QPushButton {
        text-align: right;
        padding: 12px 20px;
        border: none;
        background-color: transparent;
        color: #495057;
        font-size: 11pt;
        margin: 2px 5px;
        border-radius: 8px;
        qproperty-alignment: AlignRight;
    }
    
    #sidebar QPushButton:hover {
        background-color: #e9ecef;
        color: #2E86AB;
    }
    
    #sidebar QPushButton:checked {
        background-color: #2E86AB;
        color: white;
    }
    
    /* الأزرار */
    QPushButton {
        background-color: #2E86AB;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: bold;
        text-align: center;
        qproperty-alignment: AlignCenter;
    }
    
    QPushButton:hover {
        background-color: #1e5f82;
    }
    
    QPushButton:pressed {
        background-color: #164c6b;
    }
    
    QPushButton:disabled {
        background-color: #6c757d;
        color: #adb5bd;
    }
    
    /* حقول الإدخال */
    QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
        border: 2px solid #ced4da;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
        color: #212529;
        selection-background-color: #2E86AB;
        selection-color: white;
        text-align: right;
        qproperty-alignment: AlignRight;
    }
    
    QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
        border-color: #2E86AB;
        outline: none;
        background-color: white;
    }
    
    QComboBox::drop-down {
        border: none;
        width: 25px;
    }
    
    QComboBox::down-arrow {
        image: none;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid #495057;
        width: 0px;
        height: 0px;
    }
    
    /* الجداول */
    QTableWidget {
        gridline-color: #dee2e6;
        background-color: white;
        alternate-background-color: #f8f9fa;
        selection-background-color: #2E86AB;
        selection-color: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        color: #212529;
    }
    
    QTableWidget::item {
        padding: 8px;
        border-bottom: 1px solid #dee2e6;
        color: #212529;
        text-align: right;
        background-color: white;
    }
    
    QTableWidget::item:selected {
        background-color: #2E86AB;
        color: white;
    }
    
    QTableWidget::item:alternate {
        background-color: #f8f9fa;
    }
    
    QHeaderView::section {
        background-color: #e9ecef;
        color: #495057;
        padding: 10px;
        border: none;
        font-weight: bold;
        text-align: right;
    }
    
    /* مربعات الاختيار */
    QCheckBox {
        spacing: 8px;
        color: #495057;
        text-align: right;
    }
    
    QCheckBox::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #ced4da;
        border-radius: 3px;
        background-color: white;
    }
    
    QCheckBox::indicator:checked {
        background-color: #2E86AB;
        border-color: #2E86AB;
    }
    
    /* أزرار الراديو */
    QRadioButton {
        spacing: 8px;
        color: #495057;
        text-align: right;
    }
    
    QRadioButton::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #ced4da;
        border-radius: 9px;
        background-color: white;
    }
    
    QRadioButton::indicator:checked {
        background-color: #2E86AB;
        border-color: #2E86AB;
    }
    
    /* المجموعات */
    QGroupBox {
        font-weight: bold;
        border: 2px solid #dee2e6;
        border-radius: 8px;
        margin-top: 10px;
        padding-top: 20px;
        color: #2E86AB;
    }
    
    QGroupBox::title {
        subcontrol-origin: margin;
        right: 10px;
        padding: 0 5px 0 5px;
        color: #2E86AB;
        text-align: right;
    }
    
    /* شريط التمرير */
    QScrollBar:vertical {
        border: none;
        background: #f8f9fa;
        width: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:vertical {
        background: #ced4da;
        border-radius: 6px;
        min-height: 20px;
    }
    
    QScrollBar::handle:vertical:hover {
        background: #adb5bd;
    }
    
    /* التبويبات */
    QTabWidget::pane {
        border: 1px solid #dee2e6;
        background-color: white;
        border-radius: 6px;
    }
    
    QTabBar::tab {
        background-color: #e9ecef;
        color: #495057;
        padding: 10px 20px;
        margin-left: 2px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
    }
    
    QTabBar::tab:selected {
        background-color: #2E86AB;
        color: white;
    }
    
    QTabBar::tab:hover:!selected {
        background-color: #dee2e6;
    }
    """

def setup_arabic_ui_properties(widget):
    """إعداد خصائص واجهة المستخدم للعربية"""
    # تعيين اتجاه النص من اليمين إلى اليسار
    widget.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين الخط العربي
    arabic_font = get_arabic_font()
    widget.setFont(arabic_font)
