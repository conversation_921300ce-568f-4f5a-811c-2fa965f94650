#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مشغل التطبيق مع إخفاء رسائل التحذير
"""

import sys
import os
import subprocess

def run_with_suppressed_warnings():
    """تشغيل التطبيق مع إخفاء رسائل التحذير"""
    
    # إعداد متغيرات البيئة لإخفاء رسائل Qt
    env = os.environ.copy()
    env['QT_LOGGING_RULES'] = '*.debug=false;qt.qpa.xcb.warning=false'
    
    # تشغيل التطبيق مع إعادة توجيه stderr
    if os.name == 'nt':  # Windows
        # في Windows، استخدم subprocess مع إخفاء stderr
        with open(os.devnull, 'w') as devnull:
            subprocess.run([
                sys.executable, 'main.py'
            ], stderr=devnull, env=env, cwd=os.path.dirname(__file__))
    else:
        # في Unix/Linux/Mac
        subprocess.run([
            sys.executable, 'main.py'
        ], stderr=subprocess.DEVNULL, env=env, cwd=os.path.dirname(__file__))

if __name__ == "__main__":
    print("🚀 تشغيل منصة إدارة الحوادث المدرسية...")
    print("✨ التحسينات المطبقة:")
    print("   📝 إخفاء رسائل CSS التحذيرية")
    print("   🎨 تحسين المحاذاة العربية")
    print("   🌈 إصلاح ألوان النصوص")
    print("   📱 تحسين الخطوط العربية")
    print("-" * 60)

    try:
        run_with_suppressed_warnings()
    except KeyboardInterrupt:
        print("\n✅ تم إغلاق التطبيق بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)
