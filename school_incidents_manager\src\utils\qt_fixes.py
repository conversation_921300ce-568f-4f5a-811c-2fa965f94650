#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاحات Qt لتجنب رسائل التحذير والمشاكل الشائعة
"""

import sys
import os
from PySide6.QtCore import QLoggingCategory

def suppress_qt_warnings():
    """إخفاء رسائل التحذير غير المهمة من Qt"""
    import os

    # إخفاء رسائل CSS غير المدعومة
    QLoggingCategory.setFilterRules("qt.qpa.xcb.warning=false")
    QLoggingCategory.setFilterRules("*.debug=false")
    QLoggingCategory.setFilterRules("qt.qml.debug=false")

    # طريقة أكثر فعالية - إعادة توجيه stderr إلى devnull مؤقتاً
    if os.name == 'nt':  # Windows
        devnull = open('nul', 'w')
    else:  # Unix/Linux/Mac
        devnull = open('/dev/null', 'w')

    # حفظ stderr الأصلي
    if not hasattr(sys, '_original_stderr'):
        sys._original_stderr = sys.stderr

    # إعادة توجيه stderr إلى devnull لإخفاء رسائل box-shadow
    sys.stderr = devnull

def restore_stderr():
    """استعادة stderr الأصلي"""
    if hasattr(sys, '_original_stderr'):
        # إغلاق devnull إذا كان مفتوحاً
        if hasattr(sys.stderr, 'close'):
            sys.stderr.close()
        sys.stderr = sys._original_stderr
        delattr(sys, '_original_stderr')

def setup_qt_environment():
    """إعداد بيئة Qt المحسنة"""
    # تعيين متغيرات البيئة لتحسين الأداء
    os.environ.setdefault('QT_AUTO_SCREEN_SCALE_FACTOR', '1')
    os.environ.setdefault('QT_ENABLE_HIGHDPI_SCALING', '1')
    
    # إخفاء رسائل التحذير
    suppress_qt_warnings()

def get_optimized_stylesheet():
    """الحصول على stylesheet محسن بدون خصائص غير مدعومة"""
    return """
    QWidget {
        font-family: 'Segoe UI', 'Tahoma', Arial, sans-serif;
        font-size: 11pt;
        color: #2c3e50;
    }
    
    QMainWindow {
        background-color: #f8f9fa;
    }
    
    /* إزالة جميع خصائص box-shadow وإستبدالها ببدائل مدعومة */
    QPushButton {
        background-color: #3498db;
        color: white;
        border: 1px solid #2980b9;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: bold;
    }
    
    QPushButton:hover {
        background-color: #2980b9;
        border-color: #21618c;
    }
    
    QPushButton:pressed {
        background-color: #21618c;
        border-color: #1e5f82;
    }
    
    QLineEdit, QTextEdit, QComboBox {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
    }
    
    QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
        border-color: #3498db;
        background-color: #ffffff;
    }
    
    QTableWidget {
        border: 1px solid #dee2e6;
        border-radius: 6px;
        background-color: white;
        alternate-background-color: #f8f9fa;
        gridline-color: #dee2e6;
    }
    
    QTableWidget::item {
        padding: 8px;
        border-bottom: 1px solid #ecf0f1;
    }
    
    QTableWidget::item:selected {
        background-color: #3498db;
        color: white;
    }
    
    QHeaderView::section {
        background-color: #34495e;
        color: white;
        padding: 10px;
        border: none;
        font-weight: bold;
    }
    """
