# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QSlider, QSpinBox, QLineEdit,
                             QFrame, QGridLayout, QGroupBox, QComboBox,
                             QCheckBox, QTextEdit, QScrollArea, QFileDialog,
                             QMessageBox, QProgressBar, QTabWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QDialog, QFormLayout)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QColor
from PySide6.QtGui import QFont, QColor
import sys
import os
import shutil
from datetime import datetime

# إضافة مجلد src إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir))
sys.path.append(src_dir)

class BackupThread(QThread):
    """خيط منفصل لعمل النسخ الاحتياطية"""
    progress = Signal(int)
    finished = Signal(bool, str)
    
    def __init__(self, db_path, backup_path):
        super().__init__()
        self.db_path = db_path
        self.backup_path = backup_path
        
    def run(self):
        try:
            # نسخ قاعدة البيانات
            self.progress.emit(25)
            shutil.copy2(self.db_path, self.backup_path)
            self.progress.emit(100)
            self.finished.emit(True, "تم إنشاء النسخة الاحتياطية بنجاح")
        except Exception as e:
            self.finished.emit(False, f"حدث خطأ: {str(e)}")

class AddUserDialog(QDialog):
    """نافذة إضافة مستخدم جديد"""
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة إضافة المستخدم"""
        self.setWindowTitle("إضافة مستخدم جديد")
        self.setMinimumSize(400, 300)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # النموذج
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # اسم المستخدم
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.style_input(self.username_edit)
        form_layout.addRow("اسم المستخدم:", self.username_edit)
        
        # كلمة المرور
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.style_input(self.password_edit)
        form_layout.addRow("كلمة المرور:", self.password_edit)
        
        # تأكيد كلمة المرور
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setPlaceholderText("أعد إدخال كلمة المرور")
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.style_input(self.confirm_password_edit)
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_edit)
        
        # الدور
        self.role_combo = QComboBox()
        self.role_combo.addItems(["user", "admin"])
        self.style_combo(self.role_combo)
        form_layout.addRow("الدور:", self.role_combo)
        
        layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        button_layout = QHBoxLayout()
        
        save_button = QPushButton("💾 حفظ")
        save_button.setFixedHeight(40)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_button.clicked.connect(self.save_user)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
    def style_input(self, widget):
        """تنسيق حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_combo(self, widget):
        """تنسيق القوائم المنسدلة"""
        widget.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def save_user(self):
        """حفظ المستخدم الجديد"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        role = self.role_combo.currentText()
        
        # التحقق من البيانات
        if not username:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم")
            return
            
        if not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور")
            return
            
        if password != confirm_password:
            QMessageBox.warning(self, "خطأ", "كلمات المرور غير متطابقة")
            return
            
        if len(password) < 6:
            QMessageBox.warning(self, "خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return
            
        # التحقق من عدم تكرار اسم المستخدم
        existing = self.db_manager.execute_query("""
            SELECT id FROM users WHERE username = ?
        """, (username,), fetch=True)
        
        if existing:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم موجود مسبقاً")
            return
            
        try:
            # إضافة المستخدم
            self.db_manager.execute_query("""
                INSERT INTO users (username, password, role)
                VALUES (?, ?, ?)
            """, (username, password, role))
            
            QMessageBox.information(self, "نجح الحفظ", "تم إضافة المستخدم بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المستخدم:\n{str(e)}")

class SettingsPage(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        
    def set_academic_year(self, academic_year_id):
        """تعيين السنة الدراسية الحالية"""
        pass  # الإعدادات لا تعتمد على السنة الدراسية
        
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_users()
        
    def setup_ui(self):
        """إعداد واجهة صفحة الإعدادات"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("إعدادات التطبيق")
        title_label.setStyleSheet("""
            font-size: 28px; 
            font-weight: bold; 
            color: #2E86AB; 
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2E86AB;
        """)
        layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # إنشاء التبويبات المختلفة
        self.create_appearance_tab()
        self.create_backup_tab()
        self.create_data_tab()
        self.create_users_tab()
        self.create_about_tab()
        
        layout.addWidget(self.tabs)
        
    def create_appearance_tab(self):
        """إنشاء تبويب المظهر"""
        appearance_widget = QWidget()
        layout = QVBoxLayout(appearance_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # قسم الخط
        font_group = QGroupBox("إعدادات الخط")
        font_group.setStyleSheet(self.get_group_style("#3498db"))
        
        font_layout = QGridLayout(font_group)
        font_layout.setSpacing(15)
        font_layout.setContentsMargins(20, 30, 20, 20)
        
        # حجم الخط
        font_size_label = QLabel("حجم الخط:")
        font_size_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setRange(8, 24)
        self.font_size_slider.setValue(12)
        self.font_size_slider.valueChanged.connect(self.update_font_size)
        
        self.font_size_label = QLabel("12")
        self.font_size_label.setStyleSheet("font-weight: bold; color: #3498db;")
        self.font_size_label.setFixedWidth(30)
        
        font_layout.addWidget(font_size_label, 0, 0)
        font_layout.addWidget(self.font_size_slider, 0, 1)
        font_layout.addWidget(self.font_size_label, 0, 2)
        
        layout.addWidget(font_group)
        
        # قسم السمة
        theme_group = QGroupBox("سمة التطبيق")
        theme_group.setStyleSheet(self.get_group_style("#e74c3c"))
        
        theme_layout = QGridLayout(theme_group)
        theme_layout.setSpacing(15)
        theme_layout.setContentsMargins(20, 30, 20, 20)
        
        theme_label = QLabel("السمة:")
        theme_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن"])
        self.style_combo(self.theme_combo)
        
        theme_layout.addWidget(theme_label, 0, 0)
        theme_layout.addWidget(self.theme_combo, 0, 1)
        
        layout.addWidget(theme_group)
        
        layout.addStretch()
        
        self.tabs.addTab(appearance_widget, "🎨 المظهر")
        
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطية"""
        backup_widget = QWidget()
        layout = QVBoxLayout(backup_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # قسم النسخ الاحتياطية
        backup_group = QGroupBox("النسخ الاحتياطية لقاعدة البيانات")
        backup_group.setStyleSheet(self.get_group_style("#27ae60"))
        
        backup_layout = QVBoxLayout(backup_group)
        backup_layout.setSpacing(20)
        backup_layout.setContentsMargins(20, 30, 20, 20)
        
        # معلومات
        info_label = QLabel("""
        📝 أهمية النسخ الاحتياطية:
        • حماية البيانات من الفقدان
        • استعادة البيانات في حالة تلف قاعدة البيانات
        • أرشفة البيانات للسنوات السابقة
        
        💡 يُنصح بعمل نسخة احتياطية أسبوعياً على الأقل
        """)
        info_label.setStyleSheet("""
            color: #2c3e50;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #27ae60;
        """)
        backup_layout.addWidget(info_label)
        
        # أزرار النسخ الاحتياطية
        buttons_layout = QHBoxLayout()
        
        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.setFixedHeight(50)
        create_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        create_backup_btn.clicked.connect(self.create_backup)
        
        restore_backup_btn = QPushButton("📁 استعادة من نسخة احتياطية")
        restore_backup_btn.setFixedHeight(50)
        restore_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        restore_backup_btn.clicked.connect(self.restore_backup)
        
        buttons_layout.addWidget(create_backup_btn)
        buttons_layout.addWidget(restore_backup_btn)
        buttons_layout.addStretch()
        
        backup_layout.addLayout(buttons_layout)
        
        # شريط التقدم
        self.backup_progress = QProgressBar()
        self.backup_progress.setVisible(False)
        self.backup_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #27ae60;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 6px;
            }
        """)
        backup_layout.addWidget(self.backup_progress)
        
        layout.addWidget(backup_group)
        layout.addStretch()
        
        self.tabs.addTab(backup_widget, "💾 النسخ الاحتياطية")
        
    def create_data_tab(self):
        """إنشاء تبويب البيانات"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # قسم استيراد البيانات
        import_group = QGroupBox("استيراد البيانات")
        import_group.setStyleSheet(self.get_group_style("#f39c12"))
        
        import_layout = QVBoxLayout(import_group)
        import_layout.setSpacing(20)
        import_layout.setContentsMargins(20, 30, 20, 20)
        
        # معلومات الاستيراد
        import_info = QLabel("""
        📊 استيراد المؤسسات التعليمية من ملف Excel:
        • يجب أن يحتوي الملف على 3 أوراق (Sheets)
        • كل ورقة تمثل سلك تعليمي (ابتدائي، إعدادي، تأهيلي)
        • الأعمدة المطلوبة: اسم المؤسسة، رمز المؤسسة، الجماعة، الوسط، إلخ...
        """)
        import_info.setStyleSheet("""
            color: #2c3e50;
            background-color: #fef9e7;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #f39c12;
        """)
        import_layout.addWidget(import_info)
        
        # أزرار الاستيراد
        import_buttons_layout = QHBoxLayout()
        
        import_institutions_btn = QPushButton("📥 استيراد المؤسسات")
        import_institutions_btn.setFixedHeight(50)
        import_institutions_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        import_institutions_btn.clicked.connect(self.import_institutions)
        
        import_incidents_btn = QPushButton("📥 استيراد الحوادث")
        import_incidents_btn.setFixedHeight(50)
        import_incidents_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        import_incidents_btn.clicked.connect(self.import_incidents)
        
        import_buttons_layout.addWidget(import_institutions_btn)
        import_buttons_layout.addWidget(import_incidents_btn)
        import_buttons_layout.addStretch()
        
        import_layout.addLayout(import_buttons_layout)
        
        layout.addWidget(import_group)
        
        # قسم إدارة السنوات الدراسية
        years_group = QGroupBox("إدارة السنوات الدراسية")
        years_group.setStyleSheet(self.get_group_style("#16a085"))
        
        years_layout = QVBoxLayout(years_group)
        years_layout.setSpacing(20)
        years_layout.setContentsMargins(20, 30, 20, 20)
        
        # إضافة سنة دراسية
        add_year_layout = QHBoxLayout()
        
        year_label = QLabel("إضافة سنة دراسية:")
        year_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        
        self.new_year_edit = QLineEdit()
        self.new_year_edit.setPlaceholderText("مثال: 2025-2026")
        self.style_input(self.new_year_edit)
        
        add_year_btn = QPushButton("➕ إضافة")
        add_year_btn.setFixedHeight(40)
        add_year_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 20px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        add_year_btn.clicked.connect(self.add_academic_year)
        
        add_year_layout.addWidget(year_label)
        add_year_layout.addWidget(self.new_year_edit)
        add_year_layout.addWidget(add_year_btn)
        add_year_layout.addStretch()
        
        years_layout.addLayout(add_year_layout)
        
        layout.addWidget(years_group)
        layout.addStretch()
        
        self.tabs.addTab(data_widget, "📊 البيانات")
        
    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""
        users_widget = QWidget()
        layout = QVBoxLayout(users_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # قسم إدارة المستخدمين
        users_group = QGroupBox("إدارة مستخدمي النظام")
        users_group.setStyleSheet(self.get_group_style("#e74c3c"))
        
        users_layout = QVBoxLayout(users_group)
        users_layout.setSpacing(20)
        users_layout.setContentsMargins(20, 30, 20, 20)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        add_user_btn = QPushButton("👤 إضافة مستخدم")
        add_user_btn.setFixedHeight(40)
        add_user_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_user_btn.clicked.connect(self.add_user)
        
        refresh_users_btn = QPushButton("🔄 تحديث")
        refresh_users_btn.setFixedHeight(40)
        refresh_users_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 20px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_users_btn.clicked.connect(self.load_users)
        
        actions_layout.addWidget(add_user_btn)
        actions_layout.addWidget(refresh_users_btn)
        actions_layout.addStretch()
        
        users_layout.addLayout(actions_layout)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels(["اسم المستخدم", "الدور", "تاريخ الإنشاء", "الإجراءات"])
        self.users_table.verticalHeader().setVisible(False)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        # توزيع عرض الأعمدة
        header = self.users_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        users_layout.addWidget(self.users_table)
        
        layout.addWidget(users_group)
        
        self.tabs.addTab(users_widget, "👥 المستخدمون")
        
        # تحميل المستخدمين
        self.load_users()
        
    def create_about_tab(self):
        """إنشاء تبويب حول التطبيق"""
        about_widget = QWidget()
        layout = QVBoxLayout(about_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # معلومات التطبيق
        about_group = QGroupBox("حول التطبيق")
        about_group.setStyleSheet(self.get_group_style("#9b59b6"))
        
        about_layout = QVBoxLayout(about_group)
        about_layout.setSpacing(20)
        about_layout.setContentsMargins(20, 30, 20, 20)
        
        app_info = QLabel("""
        🏫 <b>منصة تدبير الحوادث المدرسية</b>
        
        📝 <b>الوصف:</b>
        تطبيق سطح مكتب شامل لإدارة الحوادث المدرسية باللغة العربية
        
        🔧 <b>التقنيات المستخدمة:</b>
        • Python 3.13
        • PySide6 (Qt for Python)
        • SQLite3 Database
        
        ✨ <b>المميزات:</b>
        • واجهة حديثة وعصرية
        • دعم كامل للغة العربية (RTL)
        • إدارة شاملة للحوادث والمؤسسات
        • تقارير وإحصائيات مفصلة
        • نظام نسخ احتياطية
        • إدارة متعددة المستخدمين
        
        🎯 <b>الإصدار:</b> 1.0.0
        📅 <b>تاريخ الإصدار:</b> سبتمبر 2025
        
        👨‍💻 <b>المطور:</b> فريق التطوير
        🌐 <b>الدعم:</b> متوفر باللغة العربية
        """)
        
        app_info.setStyleSheet("""
            color: #2c3e50;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #9b59b6;
            line-height: 1.6;
        """)
        app_info.setWordWrap(True)
        
        about_layout.addWidget(app_info)
        
        layout.addWidget(about_group)
        layout.addStretch()
        
        self.tabs.addTab(about_widget, "ℹ️ حول التطبيق")
        
    def get_group_style(self, color):
        """الحصول على ستايل المجموعة"""
        return f"""
            QGroupBox {{
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid {color};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """
        
    def style_input(self, widget):
        """تنسيق حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_combo(self, widget):
        """تنسيق القوائم المنسدلة"""
        widget.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def update_font_size(self, value):
        """تحديث حجم الخط"""
        self.font_size_label.setText(str(value))
        # TODO: تطبيق تغيير حجم الخط على التطبيق
        
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        # اختيار مجلد الحفظ
        backup_dir = QFileDialog.getExistingDirectory(self, "اختر مجلد حفظ النسخة الاحتياطية")
        
        if not backup_dir:
            return
            
        # إنشاء اسم الملف مع التاريخ والوقت
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"school_incidents_backup_{timestamp}.db"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # إظهار شريط التقدم
        self.backup_progress.setVisible(True)
        self.backup_progress.setValue(0)
        
        # إنشاء خيط النسخ الاحتياطية
        self.backup_thread = BackupThread(self.db_manager.db_path, backup_path)
        self.backup_thread.progress.connect(self.backup_progress.setValue)
        self.backup_thread.finished.connect(self.backup_finished)
        self.backup_thread.start()
        
    def backup_finished(self, success, message):
        """عند انتهاء النسخ الاحتياطية"""
        self.backup_progress.setVisible(False)
        
        if success:
            QMessageBox.information(self, "نجح النسخ الاحتياطي", message)
        else:
            QMessageBox.critical(self, "خطأ في النسخ الاحتياطي", message)
            
    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        backup_file, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف النسخة الاحتياطية", "", "Database Files (*.db)")
        
        if not backup_file:
            return
            
        reply = QMessageBox.question(self, "تأكيد الاستعادة",
                                   "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n"
                                   "سيتم استبدال البيانات الحالية نهائياً!",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                shutil.copy2(backup_file, self.db_manager.db_path)
                QMessageBox.information(self, "نجحت الاستعادة", 
                                      "تم استعادة النسخة الاحتياطية بنجاح\n"
                                      "يرجى إعادة تشغيل التطبيق")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الاستعادة:\n{str(e)}")
                
    def import_institutions(self):
        """استيراد المؤسسات من ملف Excel"""
        QMessageBox.information(self, "قريباً", "ميزة استيراد المؤسسات ستتوفر قريباً")
        
    def import_incidents(self):
        """استيراد الحوادث من ملف Excel"""
        QMessageBox.information(self, "قريباً", "ميزة استيراد الحوادث ستتوفر قريباً")
        
    def add_academic_year(self):
        """إضافة سنة دراسية جديدة"""
        year_name = self.new_year_edit.text().strip()
        
        if not year_name:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم السنة الدراسية")
            return
            
        # التحقق من صحة التنسيق
        if not (len(year_name) == 9 and year_name[4] == '-'):
            QMessageBox.warning(self, "خطأ", "تنسيق السنة الدراسية غير صحيح\nمثال: 2025-2026")
            return
            
        try:
            # التحقق من عدم التكرار
            existing = self.db_manager.execute_query("""
                SELECT id FROM academic_years WHERE year_name = ?
            """, (year_name,), fetch=True)
            
            if existing:
                QMessageBox.warning(self, "خطأ", "السنة الدراسية موجودة مسبقاً")
                return
                
            # إضافة السنة الدراسية
            self.db_manager.execute_query("""
                INSERT INTO academic_years (year_name) VALUES (?)
            """, (year_name,))
            
            QMessageBox.information(self, "نجح الحفظ", f"تم إضافة السنة الدراسية {year_name} بنجاح")
            self.new_year_edit.clear()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة السنة الدراسية:\n{str(e)}")
            
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = AddUserDialog(self.db_manager, self)
        if dialog.exec() == QDialog.Accepted:
            self.load_users()
            
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        users = self.db_manager.execute_query("""
            SELECT username, role, created_at, id FROM users ORDER BY created_at DESC
        """, fetch=True)
        
        self.users_table.setRowCount(len(users) if users else 0)
        
        if users:
            for row, (username, role, created_at, user_id) in enumerate(users):
                # اسم المستخدم
                username_item = QTableWidgetItem(str(username))
                username_item.setForeground(QColor("#2c3e50"))
                self.users_table.setItem(row, 0, username_item)
                
                # الدور
                role_item = QTableWidgetItem(str(role))
                if role == "admin":
                    role_item.setForeground(QColor("#e74c3c"))
                    role_item.setText("مدير")
                else:
                    role_item.setForeground(QColor("#3498db"))
                    role_item.setText("مستخدم")
                self.users_table.setItem(row, 1, role_item)
                
                # تاريخ الإنشاء
                date_item = QTableWidgetItem(str(created_at))
                date_item.setForeground(QColor("#2c3e50"))
                self.users_table.setItem(row, 2, date_item)
                
                # أزرار الإجراءات
                if username != "admin":  # لا يمكن حذف المدير الرئيسي
                    actions_widget = QWidget()
                    actions_layout = QHBoxLayout(actions_widget)
                    actions_layout.setContentsMargins(5, 5, 5, 5)
                    
                    delete_btn = QPushButton("حذف")
                    delete_btn.setFixedSize(60, 25)
                    delete_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #e74c3c;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            font-size: 10px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #c0392b;
                        }
                    """)
                    delete_btn.clicked.connect(lambda checked, uid=user_id: self.delete_user(uid))
                    
                    actions_layout.addWidget(delete_btn)
                    self.users_table.setCellWidget(row, 3, actions_widget)
                    
        # توسيط النص في الخلايا
        for row in range(self.users_table.rowCount()):
            for col in range(self.users_table.columnCount() - 1):
                item = self.users_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    
    def delete_user(self, user_id):
        """حذف مستخدم"""
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا المستخدم؟",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("DELETE FROM users WHERE id = ?", (user_id,))
                QMessageBox.information(self, "تم الحذف", "تم حذف المستخدم بنجاح")
                self.load_users()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المستخدم:\n{str(e)}")
