# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QComboBox, QPushButton, QTableWidget,
                             QTableWidgetItem, QHeaderView, QFrame, QGridLayout,
                             QGroupBox, QDateEdit, QMessageBox, QDialog,
                             QTextEdit, QScrollArea, QSplitter)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QColor, QIcon, QPixmap
from PySide6.QtSvg import QSvgRenderer
import sys
import os

# إضافة مجلد src إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir))
sys.path.append(src_dir)

class IncidentDetailsDialog(QDialog):
    """نافذة تفاصيل الحادثة"""
    def __init__(self, incident_data, parent=None):
        super().__init__(parent)
        self.incident_data = incident_data
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة تفاصيل الحادثة"""
        self.setWindowTitle("تفاصيل الحادثة")
        self.setMinimumSize(600, 700)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان النافذة
        title_label = QLabel(f"تفاصيل الحادثة - ملف رقم: {self.incident_data.get('file_number', 'غير محدد')}")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #2E86AB;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2E86AB;
        """)
        layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # معلومات المتضرر
        self.create_victim_details(content_layout)
        
        # معلومات المؤسسة
        self.create_institution_details(content_layout)
        
        # معلومات الحادثة
        self.create_incident_details(content_layout)
        
        # معلومات المستفيدين
        self.create_beneficiaries_details(content_layout)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area, 1)
        
        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setFixedHeight(40)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        close_button.clicked.connect(self.accept)
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)
        
    def create_victim_details(self, parent_layout):
        """إنشاء قسم تفاصيل المتضرر"""
        group = QGroupBox("معلومات المتضرر")
        group.setStyleSheet(self.get_group_style("#3498db"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        details = [
            ("الاسم والنسب:", self.incident_data.get('victim_name', 'غير محدد')),
            ("رقم المسار:", self.incident_data.get('student_path_number', 'غير محدد')),
            ("النوع:", self.incident_data.get('gender', 'غير محدد'))
        ]
        
        for i, (label_text, value) in enumerate(details):
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #2c3e50;")
            value_label = QLabel(str(value))
            value_label.setStyleSheet("color: #34495e; font-size: 14px;")
            
            layout.addWidget(label, i, 0)
            layout.addWidget(value_label, i, 1)
            
        parent_layout.addWidget(group)
        
    def create_institution_details(self, parent_layout):
        """إنشاء قسم تفاصيل المؤسسة"""
        group = QGroupBox("معلومات المؤسسة")
        group.setStyleSheet(self.get_group_style("#e74c3c"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        details = [
            ("اسم المؤسسة:", self.incident_data.get('institution_name', 'غير محدد')),
            ("الوسط:", self.incident_data.get('environment', 'غير محدد')),
            ("السلك:", self.incident_data.get('level', 'غير محدد'))
        ]
        
        for i, (label_text, value) in enumerate(details):
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #2c3e50;")
            value_label = QLabel(str(value))
            value_label.setStyleSheet("color: #34495e; font-size: 14px;")
            
            layout.addWidget(label, i, 0)
            layout.addWidget(value_label, i, 1)
            
        parent_layout.addWidget(group)
        
    def create_incident_details(self, parent_layout):
        """إنشاء قسم تفاصيل الحادثة"""
        group = QGroupBox("معلومات الحادثة")
        group.setStyleSheet(self.get_group_style("#f39c12"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        details = [
            ("المرجع:", self.incident_data.get('reference', 'غير محدد')),
            ("تاريخ الحادثة:", self.incident_data.get('incident_date', 'غير محدد')),
            ("تاريخ التسوية:", self.incident_data.get('settlement_date', 'غير محدد')),
            ("الحالة:", self.incident_data.get('status', 'غير محدد')),
            ("نوع الحادثة:", self.incident_data.get('incident_type', 'غير محدد')),
            ("خطورة الحادثة:", self.incident_data.get('severity', 'غير محدد'))
        ]
        
        for i, (label_text, value) in enumerate(details):
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #2c3e50;")
            value_label = QLabel(str(value))
            
            # تلوين الحالة
            if label_text == "الحالة:":
                if value == "تمت التسوية":
                    value_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 14px;")
                elif value == "في طور التسوية":
                    value_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 14px;")
                elif value == "مرفوض":
                    value_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
                elif value == "تم الدفع":
                    value_label.setStyleSheet("color: #9b59b6; font-weight: bold; font-size: 14px;")
                else:
                    value_label.setStyleSheet("color: #34495e; font-size: 14px;")
            else:
                value_label.setStyleSheet("color: #34495e; font-size: 14px;")
            
            layout.addWidget(label, i, 0)
            layout.addWidget(value_label, i, 1)
            
        parent_layout.addWidget(group)
        
    def create_beneficiaries_details(self, parent_layout):
        """إنشاء قسم تفاصيل المستفيدين"""
        group = QGroupBox("المستفيدون من التعويض")
        group.setStyleSheet(self.get_group_style("#27ae60"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        details = [
            ("الاسم والنسب:", self.incident_data.get('beneficiary_name', 'غير محدد')),
            ("الهاتف النقال:", self.incident_data.get('mobile_phone', 'غير محدد')),
            ("صلة القرابة:", self.get_relationship())
        ]
        
        for i, (label_text, value) in enumerate(details):
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #2c3e50;")
            value_label = QLabel(str(value))
            value_label.setStyleSheet("color: #34495e; font-size: 14px;")
            
            layout.addWidget(label, i, 0)
            layout.addWidget(value_label, i, 1)
            
        parent_layout.addWidget(group)
        
    def get_relationship(self):
        """الحصول على صلة القرابة"""
        relationships = []
        if self.incident_data.get('is_father'):
            relationships.append("الأب")
        if self.incident_data.get('is_mother'):
            relationships.append("الأم")
        if self.incident_data.get('is_guardian'):
            relationships.append("الولي")
        
        return " - ".join(relationships) if relationships else "غير محدد"
        
    def get_group_style(self, color):
        """الحصول على ستايل المجموعة"""
        return f"""
            QGroupBox {{
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid {color};
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """

class IncidentsListPage(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.academic_year_id = None
        self.setup_ui()
        
    def create_svg_icon(self, svg_name, size=20):
        """إنشاء أيقونة من ملف SVG"""
        try:
            # مسار الأيقونة
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_dir = os.path.dirname(os.path.dirname(current_dir))
            svg_path = os.path.join(project_dir, "resources", "icons", f"{svg_name}.svg")
            
            # التحقق من وجود الملف
            if not os.path.exists(svg_path):
                return QIcon()
                
            # قراءة محتوى SVG مباشرة
            with open(svg_path, 'r', encoding='utf-8') as f:
                svg_content = f.read()
            
            # إنشاء QIcon من SVG باستخدام bytes
            svg_bytes = svg_content.encode('utf-8')
            renderer = QSvgRenderer(svg_bytes)
            
            if not renderer.isValid():
                return QIcon()
                
            # إنشاء pixmap
            pixmap = QPixmap(size, size)
            pixmap.fill(Qt.transparent)
            
            from PySide6.QtGui import QPainter
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            return QIcon(pixmap)
            
        except Exception as e:
            return QIcon()
        
    def set_academic_year(self, academic_year_id):
        """تعيين السنة الدراسية الحالية"""
        self.academic_year_id = academic_year_id
        self.load_incidents(academic_year_id)
        
    def refresh_data(self):
        """تحديث البيانات"""
        if self.academic_year_id:
            self.load_incidents(self.academic_year_id)
        
    def setup_ui(self):
        """إعداد واجهة قائمة الحوادث"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("قائمة الحوادث المدرسية")
        title_label.setStyleSheet("""
            font-size: 28px; 
            font-weight: bold; 
            color: #2E86AB; 
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2E86AB;
        """)
        layout.addWidget(title_label)
        
        # إنشاء قسم البحث والتصفية
        self.create_filter_section(layout)
        
        # إنشاء جدول الحوادث
        self.create_incidents_table(layout)
        
    def create_filter_section(self, parent_layout):
        """إنشاء قسم البحث والتصفية"""
        filter_group = QGroupBox("البحث والتصفية")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        filter_layout = QGridLayout(filter_group)
        filter_layout.setSpacing(15)
        filter_layout.setContentsMargins(20, 30, 20, 20)
        
        # البحث بالاسم
        search_label = QLabel("البحث بالاسم:")
        search_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم...")
        self.style_input(self.search_edit)
        self.search_edit.textChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(search_label, 0, 0)
        filter_layout.addWidget(self.search_edit, 0, 1)
        
        # تصفية حسب النوع
        gender_label = QLabel("النوع:")
        gender_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.gender_filter = QComboBox()
        self.gender_filter.addItems(["الكل", "ذكر", "أنثى"])
        self.style_combo(self.gender_filter)
        self.gender_filter.currentTextChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(gender_label, 0, 2)
        filter_layout.addWidget(self.gender_filter, 0, 3)
        
        # تصفية حسب الوسط
        env_label = QLabel("الوسط:")
        env_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.environment_filter = QComboBox()
        self.environment_filter.addItems(["الكل", "قروي", "حضري"])
        self.style_combo(self.environment_filter)
        self.environment_filter.currentTextChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(env_label, 1, 0)
        filter_layout.addWidget(self.environment_filter, 1, 1)
        
        # تصفية حسب السلك
        level_label = QLabel("السلك:")
        level_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.level_filter = QComboBox()
        self.level_filter.addItems(["الكل", "ابتدائي", "إعدادي", "تأهيلي"])
        self.style_combo(self.level_filter)
        self.level_filter.currentTextChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(level_label, 1, 2)
        filter_layout.addWidget(self.level_filter, 1, 3)
        
        # تصفية حسب نوع الحادثة
        type_label = QLabel("نوع الحادثة:")
        type_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.incident_type_filter = QComboBox()
        self.incident_type_filter.addItems(["الكل", "حادثة مدرسية", "حادثة تنقل", "حادثة رياضية"])
        self.style_combo(self.incident_type_filter)
        self.incident_type_filter.currentTextChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(type_label, 2, 0)
        filter_layout.addWidget(self.incident_type_filter, 2, 1)
        
        # تصفية حسب الحالة
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "في طور التسوية", "تمت التسوية", "مرفوض", "تم الدفع"])
        self.style_combo(self.status_filter)
        self.status_filter.currentTextChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(status_label, 2, 2)
        filter_layout.addWidget(self.status_filter, 2, 3)
        
        # تصفية حسب التاريخ
        date_from_label = QLabel("من تاريخ:")
        date_from_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setDate(QDate(2018, 1, 1))  # تاريخ يشمل جميع البيانات الافتراضية
        self.date_from_edit.setCalendarPopup(True)
        self.style_date(self.date_from_edit)
        self.date_from_edit.dateChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(date_from_label, 3, 0)
        filter_layout.addWidget(self.date_from_edit, 3, 1)
        
        date_to_label = QLabel("إلى تاريخ:")
        date_to_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setDate(QDate.currentDate().addYears(1))  # تاريخ مستقبلي لتغطية جميع البيانات
        self.date_to_edit.setCalendarPopup(True)
        self.style_date(self.date_to_edit)
        self.date_to_edit.dateChanged.connect(self.filter_incidents)
        
        filter_layout.addWidget(date_to_label, 3, 2)
        filter_layout.addWidget(self.date_to_edit, 3, 3)
        
        # أزرار الإجراءات
        button_layout = QHBoxLayout()
        
        reset_button = QPushButton("🔄 إعادة تعيين التصفية")
        reset_button.setFixedHeight(40)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 20px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        reset_button.clicked.connect(self.reset_filters)
        
        refresh_button = QPushButton("🔃 تحديث")
        refresh_button.setFixedHeight(40)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        refresh_button.clicked.connect(self.load_incidents)
        
        button_layout.addStretch()
        button_layout.addWidget(reset_button)
        button_layout.addWidget(refresh_button)
        
        filter_layout.addLayout(button_layout, 4, 0, 1, 4)
        
        parent_layout.addWidget(filter_group)
        
    def create_incidents_table(self, parent_layout):
        """إنشاء جدول الحوادث"""
        # إحصائيات سريعة
        stats_layout = QHBoxLayout()
        
        self.total_label = QLabel("إجمالي الحوادث: 0")
        self.total_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            padding: 8px 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        
        stats_layout.addWidget(self.total_label)
        stats_layout.addStretch()
        
        parent_layout.addLayout(stats_layout)
        
        # الجدول
        self.incidents_table = QTableWidget()
        self.incidents_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #ecf0f1;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        # أعمدة الجدول
        headers = [
            "رقم الملف", "الاسم والنسب", "النوع", "المؤسسة", 
            "السلك", "تاريخ الحادثة", "الحالة", "الإجراءات"
        ]
        
        self.incidents_table.setColumnCount(len(headers))
        self.incidents_table.setHorizontalHeaderLabels(headers)
        
        # إعداد الجدول
        self.incidents_table.setAlternatingRowColors(True)
        self.incidents_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.incidents_table.verticalHeader().setVisible(False)
        
        # توزيع عرض الأعمدة
        header = self.incidents_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم الملف
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # المؤسسة
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # السلك
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # الحالة
        header.setSectionResizeMode(7, QHeaderView.Fixed)            # الإجراءات
        header.resizeSection(7, 260)  # تعيين عرض ثابت لعمود الإجراءات
        
        # ربط النقر على الصف
        self.incidents_table.cellDoubleClicked.connect(self.show_incident_details)
        
        parent_layout.addWidget(self.incidents_table, 1)
        
    def style_input(self, widget):
        """تنسيق حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_combo(self, widget):
        """تنسيق القوائم المنسدلة"""
        widget.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #2c3e50;
                width: 0px;
                height: 0px;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_date(self, widget):
        """تنسيق حقول التاريخ"""
        widget.setStyleSheet("""
            QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QDateEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def load_incidents(self, academic_year_id=None):
        """تحميل الحوادث للسنة الدراسية المحددة"""
        if academic_year_id:
            self.academic_year_id = academic_year_id
            
        if not self.academic_year_id:
            return
            
        # استعلام الحوادث مع معلومات المؤسسة
        query = """
            SELECT 
                i.id,
                i.file_number,
                i.victim_name,
                i.gender,
                ei.institution_name,
                ei.level,
                i.incident_date,
                i.status,
                i.student_path_number,
                ei.environment,
                i.reference,
                i.settlement_date,
                i.incident_type,
                i.severity
            FROM incidents i
            JOIN educational_institutions ei ON i.institution_id = ei.id
            WHERE i.academic_year_id = ?
            ORDER BY i.incident_date DESC
        """
        
        self.all_incidents = self.db_manager.execute_query(query, (self.academic_year_id,), fetch=True)
        self.filter_incidents()
        
    def filter_incidents(self):
        """تطبيق التصفية على الحوادث"""
        if not hasattr(self, 'all_incidents') or not self.all_incidents:
            return
            
        filtered_incidents = []
        
        search_text = self.search_edit.text().lower()
        gender_filter = self.gender_filter.currentText()
        env_filter = self.environment_filter.currentText()
        level_filter = self.level_filter.currentText()
        type_filter = self.incident_type_filter.currentText()
        status_filter = self.status_filter.currentText()
        date_from = self.date_from_edit.date().toPython()
        date_to = self.date_to_edit.date().toPython()
        
        for incident in self.all_incidents:
            # تطبيق التصفية
            if search_text and search_text not in incident[2].lower():  # البحث بالاسم
                continue
                
            if gender_filter != "الكل" and incident[3] != gender_filter:  # النوع
                continue
                
            if env_filter != "الكل" and incident[9] != env_filter:  # الوسط
                continue
                
            if level_filter != "الكل" and incident[5] != level_filter:  # السلك
                continue
                
            if type_filter != "الكل" and incident[12] != type_filter:  # نوع الحادثة
                continue
                
            if status_filter != "الكل" and incident[7] != status_filter:  # الحالة
                continue
                
            # تصفية التاريخ
            try:
                incident_date = QDate.fromString(incident[6], "yyyy-MM-dd").toPython()
                if incident_date < date_from or incident_date > date_to:
                    continue
            except:
                pass
                
            filtered_incidents.append(incident)
            
        self.display_incidents(filtered_incidents)
        
    def display_incidents(self, incidents):
        """عرض الحوادث في الجدول"""
        self.incidents_table.setRowCount(len(incidents))
        
        for row, incident in enumerate(incidents):
            # رقم الملف
            file_item = QTableWidgetItem(str(incident[1]))
            file_item.setForeground(QColor("#2c3e50"))
            self.incidents_table.setItem(row, 0, file_item)
            
            # الاسم والنسب
            name_item = QTableWidgetItem(str(incident[2]))
            name_item.setForeground(QColor("#2c3e50"))
            self.incidents_table.setItem(row, 1, name_item)
            
            # النوع
            gender_item = QTableWidgetItem(str(incident[3]))
            if incident[3] == "ذكر":
                gender_item.setForeground(QColor("#3498db"))
            else:
                gender_item.setForeground(QColor("#e74c3c"))
            self.incidents_table.setItem(row, 2, gender_item)
            
            # المؤسسة
            institution_item = QTableWidgetItem(str(incident[4]))
            institution_item.setForeground(QColor("#2c3e50"))
            self.incidents_table.setItem(row, 3, institution_item)
            
            # السلك
            level_item = QTableWidgetItem(str(incident[5]))
            if incident[5] == "ابتدائي":
                level_item.setForeground(QColor("#27ae60"))
            elif incident[5] == "إعدادي":
                level_item.setForeground(QColor("#f39c12"))
            else:  # تأهيلي
                level_item.setForeground(QColor("#9b59b6"))
            self.incidents_table.setItem(row, 4, level_item)
            
            # تاريخ الحادثة
            date_item = QTableWidgetItem(str(incident[6]))
            date_item.setForeground(QColor("#2c3e50"))
            self.incidents_table.setItem(row, 5, date_item)
            
            # الحالة
            status_item = QTableWidgetItem(str(incident[7]))
            if incident[7] == "تمت التسوية":
                status_item.setForeground(QColor("#27ae60"))
                status_item.setFont(QFont("Arial", 12, QFont.Bold))
            elif incident[7] == "في طور التسوية":
                status_item.setForeground(QColor("#f39c12"))
                status_item.setFont(QFont("Arial", 12, QFont.Bold))
            elif incident[7] == "مرفوض":
                status_item.setForeground(QColor("#e74c3c"))
                status_item.setFont(QFont("Arial", 12, QFont.Bold))
            elif incident[7] == "تم الدفع":
                status_item.setForeground(QColor("#9b59b6"))
                status_item.setFont(QFont("Arial", 12, QFont.Bold))
            self.incidents_table.setItem(row, 6, status_item)
            
            # أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            actions_layout.setSpacing(5)
            
            # زر التفاصيل
            details_btn = QPushButton("عرض")
            details_icon = self.create_svg_icon("view", 18)
            if not details_icon.isNull():
                details_btn.setIcon(details_icon)
                details_btn.setIconSize(details_btn.size() * 0.6)
            details_btn.setFixedSize(75, 35)
            details_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 11px;
                    font-weight: bold;
                    padding: 4px;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                    border: 2px solid #1abc9c;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
            details_btn.clicked.connect(lambda checked, r=row: self.show_incident_details(r, 0))
            
            # زر التعديل
            edit_btn = QPushButton("تعديل")
            edit_icon = self.create_svg_icon("edit", 18)
            if not edit_icon.isNull():
                edit_btn.setIcon(edit_icon)
                edit_btn.setIconSize(edit_btn.size() * 0.6)
            edit_btn.setFixedSize(75, 35)
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 11px;
                    font-weight: bold;
                    padding: 4px;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                    border: 2px solid #1abc9c;
                }
                QPushButton:pressed {
                    background-color: #b7950b;
                }
            """)
            edit_btn.clicked.connect(lambda checked, r=row: self.edit_incident(r))
            
            # زر الحذف
            delete_btn = QPushButton("حذف")
            delete_icon = self.create_svg_icon("delete", 18)
            if not delete_icon.isNull():
                delete_btn.setIcon(delete_icon)
                delete_btn.setIconSize(delete_btn.size() * 0.6)
            delete_btn.setFixedSize(75, 35)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 11px;
                    font-weight: bold;
                    padding: 4px;
                    text-align: center;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                    border: 2px solid #1abc9c;
                }
                QPushButton:pressed {
                    background-color: #922b21;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_incident(r))
            
            actions_layout.addWidget(details_btn)
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            
            self.incidents_table.setCellWidget(row, 7, actions_widget)
            
        # توسيط النص في الخلايا
        for row in range(self.incidents_table.rowCount()):
            for col in range(self.incidents_table.columnCount() - 1):  # ماعدا عمود الإجراءات
                item = self.incidents_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    
        # تحديث الإحصائيات
        self.total_label.setText(f"إجمالي الحوادث: {len(incidents)}")
        
    def show_incident_details(self, row, column=None):
        """عرض تفاصيل الحادثة"""
        if not hasattr(self, 'all_incidents') or not self.all_incidents:
            return
            
        # الحصول على معرف الحادثة
        file_number = self.incidents_table.item(row, 0).text()
        
        # البحث عن الحادثة في البيانات
        incident = None
        for inc in self.all_incidents:
            if inc[1] == file_number:
                incident = inc
                break
                
        if not incident:
            return
            
        # الحصول على بيانات المستفيد
        beneficiary = self.db_manager.execute_query("""
            SELECT is_father, is_mother, is_guardian, full_name, mobile_phone
            FROM beneficiaries WHERE incident_id = ?
        """, (incident[0],), fetch=True)
        
        # تحضير البيانات للنافذة
        incident_data = {
            'file_number': incident[1],
            'victim_name': incident[2],
            'student_path_number': incident[8],
            'gender': incident[3],
            'institution_name': incident[4],
            'environment': incident[9],
            'level': incident[5],
            'reference': incident[10],
            'incident_date': incident[6],
            'settlement_date': incident[11],
            'status': incident[7],
            'incident_type': incident[12],
            'severity': incident[13]
        }
        
        if beneficiary and beneficiary[0]:
            ben_data = beneficiary[0]
            incident_data.update({
                'is_father': ben_data[0],
                'is_mother': ben_data[1],
                'is_guardian': ben_data[2],
                'beneficiary_name': ben_data[3],
                'mobile_phone': ben_data[4]
            })
        
        # عرض نافذة التفاصيل
        dialog = IncidentDetailsDialog(incident_data, self)
        dialog.exec()
        
    def edit_incident(self, row):
        """تعديل الحادثة"""
        QMessageBox.information(self, "قريباً", "ستتوفر هذه الميزة قريباً")
        
    def delete_incident(self, row):
        """حذف الحادثة"""
        file_number = self.incidents_table.item(row, 0).text()
        victim_name = self.incidents_table.item(row, 1).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الحادثة؟\n"
                                   f"رقم الملف: {file_number}\n"
                                   f"المتضرر: {victim_name}\n\n"
                                   f"تحذير: لا يمكن التراجع عن هذا الإجراء!",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                # حذف الحادثة من قاعدة البيانات
                self.db_manager.execute_query("""
                    DELETE FROM incidents WHERE file_number = ?
                """, (file_number,))
                
                QMessageBox.information(self, "تم الحذف", "تم حذف الحادثة بنجاح")
                self.load_incidents()  # إعادة تحميل البيانات
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الحادثة:\n{str(e)}")
        
    def reset_filters(self):
        """إعادة تعيين التصفية"""
        self.search_edit.clear()
        self.gender_filter.setCurrentIndex(0)
        self.environment_filter.setCurrentIndex(0)
        self.level_filter.setCurrentIndex(0)
        self.incident_type_filter.setCurrentIndex(0)
        self.status_filter.setCurrentIndex(0)
        self.date_from_edit.setDate(QDate(2018, 1, 1))  # نفس التاريخ الافتراضي
        self.date_to_edit.setDate(QDate.currentDate().addYears(1))
        self.filter_incidents()
