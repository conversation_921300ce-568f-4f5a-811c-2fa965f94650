# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QComboBox, QDateEdit, QTextEdit,
                             QPushButton, QFrame, QGridLayout, QGroupBox,
                             QCheckBox, QScrollArea, QMessageBox)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont
import sys
import os

# إضافة مجلد src إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir))
sys.path.append(src_dir)

class AddIncidentPage(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.academic_year_id = None
        self.setup_ui()
        
    def set_academic_year(self, academic_year_id):
        """تعيين السنة الدراسية الحالية"""
        self.academic_year_id = academic_year_id
        self.load_institutions(academic_year_id)
        
    def refresh_data(self):
        """تحديث البيانات"""
        if self.academic_year_id:
            self.load_institutions(self.academic_year_id)
        
    def setup_ui(self):
        """إعداد واجهة إضافة الحوادث"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("إضافة حادثة جديدة")
        title_label.setStyleSheet("""
            font-size: 28px; 
            font-weight: bold; 
            color: #2E86AB; 
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2E86AB;
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        
        # الويدجت الرئيسي داخل منطقة التمرير
        main_widget = QWidget()
        scroll_area.setWidget(main_widget)
        
        # تخطيط المحتوى
        layout = QVBoxLayout(main_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(25)
        
        # إنشاء الأقسام
        self.create_victim_section(layout)
        self.create_institution_section(layout)
        self.create_incident_section(layout)
        self.create_beneficiaries_section(layout)
        
        # أزرار الحفظ والإلغاء
        self.create_action_buttons(layout)
        
        main_layout.addWidget(scroll_area)
        
    def create_victim_section(self, parent_layout):
        """إنشاء قسم معلومات المتضرر"""
        group = QGroupBox("معلومات المتضرر")
        group.setStyleSheet(self.get_group_style("#3498db"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 30, 20, 20)
        
        # الاسم والنسب (إجباري)
        name_label = QLabel("الاسم والنسب *:")
        name_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.victim_name_edit = QLineEdit()
        self.victim_name_edit.setPlaceholderText("أدخل الاسم الكامل للمتضرر")
        self.style_input(self.victim_name_edit)
        
        layout.addWidget(name_label, 0, 0)
        layout.addWidget(self.victim_name_edit, 0, 1)
        
        # رقم مسار (اختياري)
        path_label = QLabel("رقم مسار:")
        path_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.student_path_edit = QLineEdit()
        self.student_path_edit.setPlaceholderText("رقم مسار التلميذ (اختياري)")
        self.style_input(self.student_path_edit)
        
        layout.addWidget(path_label, 1, 0)
        layout.addWidget(self.student_path_edit, 1, 1)
        
        # النوع (إجباري)
        gender_label = QLabel("النوع *:")
        gender_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.gender_combo = QComboBox()
        self.gender_combo.addItems(["اختر النوع", "ذكر", "أنثى"])
        self.style_combo(self.gender_combo)
        
        layout.addWidget(gender_label, 2, 0)
        layout.addWidget(self.gender_combo, 2, 1)
        
        parent_layout.addWidget(group)
        
    def create_institution_section(self, parent_layout):
        """إنشاء قسم معلومات المؤسسة"""
        group = QGroupBox("معلومات المؤسسة")
        group.setStyleSheet(self.get_group_style("#e74c3c"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 30, 20, 20)
        
        # اسم المؤسسة (إجباري)
        inst_label = QLabel("اسم المؤسسة *:")
        inst_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.institution_combo = QComboBox()
        self.institution_combo.setEditable(True)
        self.institution_combo.setPlaceholderText("اختر أو أدخل اسم المؤسسة")
        self.style_combo(self.institution_combo)
        
        layout.addWidget(inst_label, 0, 0)
        layout.addWidget(self.institution_combo, 0, 1)
        
        # الوسط (إجباري)
        env_label = QLabel("الوسط *:")
        env_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.environment_combo = QComboBox()
        self.environment_combo.addItems(["اختر الوسط", "قروي", "حضري"])
        self.style_combo(self.environment_combo)
        
        layout.addWidget(env_label, 1, 0)
        layout.addWidget(self.environment_combo, 1, 1)
        
        # السلك (إجباري)
        level_label = QLabel("السلك *:")
        level_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.level_combo = QComboBox()
        self.level_combo.addItems(["اختر السلك", "ابتدائي", "إعدادي", "تأهيلي"])
        self.style_combo(self.level_combo)
        
        layout.addWidget(level_label, 2, 0)
        layout.addWidget(self.level_combo, 2, 1)
        
        parent_layout.addWidget(group)
        
    def create_incident_section(self, parent_layout):
        """إنشاء قسم معلومات الحادثة"""
        group = QGroupBox("معلومات الحادثة")
        group.setStyleSheet(self.get_group_style("#f39c12"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 30, 20, 20)
        
        # المرجع (اختياري)
        ref_label = QLabel("المرجع:")
        ref_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.reference_edit = QLineEdit()
        self.reference_edit.setPlaceholderText("مرجع الحادثة (نص وأرقام)")
        self.style_input(self.reference_edit)
        
        layout.addWidget(ref_label, 0, 0)
        layout.addWidget(self.reference_edit, 0, 1)
        
        # تاريخ الحادثة (إجباري)
        incident_date_label = QLabel("تاريخ الحادثة *:")
        incident_date_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.incident_date_edit = QDateEdit()
        self.incident_date_edit.setDate(QDate.currentDate())
        self.incident_date_edit.setCalendarPopup(True)
        self.style_date(self.incident_date_edit)
        
        layout.addWidget(incident_date_label, 1, 0)
        layout.addWidget(self.incident_date_edit, 1, 1)
        
        # تاريخ التسوية (إجباري)
        settlement_date_label = QLabel("تاريخ التسوية *:")
        settlement_date_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.settlement_date_edit = QDateEdit()
        self.settlement_date_edit.setDate(QDate.currentDate().addDays(30))
        self.settlement_date_edit.setCalendarPopup(True)
        self.style_date(self.settlement_date_edit)
        
        layout.addWidget(settlement_date_label, 2, 0)
        layout.addWidget(self.settlement_date_edit, 2, 1)
        
        # الحالة (إجباري)
        status_label = QLabel("الحالة *:")
        status_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "اختر الحالة", "في طور التسوية", "تمت التسوية", "مرفوض", "تم الدفع"
        ])
        self.style_combo(self.status_combo)
        
        layout.addWidget(status_label, 3, 0)
        layout.addWidget(self.status_combo, 3, 1)
        
        # نوع الحادثة (إجباري)
        type_label = QLabel("نوع الحادثة *:")
        type_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.incident_type_combo = QComboBox()
        self.incident_type_combo.addItems([
            "اختر نوع الحادثة", "حادثة مدرسية", "حادثة تنقل", "حادثة رياضية"
        ])
        self.style_combo(self.incident_type_combo)
        
        layout.addWidget(type_label, 4, 0)
        layout.addWidget(self.incident_type_combo, 4, 1)
        
        # خطورة الحادثة (اختياري)
        severity_label = QLabel("خطورة الحادثة:")
        severity_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.severity_combo = QComboBox()
        self.severity_combo.addItems(["غير محدد", "عادية", "خطيرة", "وفاة"])
        self.style_combo(self.severity_combo)
        
        layout.addWidget(severity_label, 5, 0)
        layout.addWidget(self.severity_combo, 5, 1)
        
        parent_layout.addWidget(group)
        
    def create_beneficiaries_section(self, parent_layout):
        """إنشاء قسم المستفيدين من التعويض"""
        group = QGroupBox("المستفيدون من التعويض")
        group.setStyleSheet(self.get_group_style("#27ae60"))
        
        layout = QGridLayout(group)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 30, 20, 20)
        
        # خانات الاختيار
        checkbox_layout = QHBoxLayout()
        
        self.father_checkbox = QCheckBox("الأب")
        self.mother_checkbox = QCheckBox("الأم")
        self.guardian_checkbox = QCheckBox("الولي")
        
        for checkbox in [self.father_checkbox, self.mother_checkbox, self.guardian_checkbox]:
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-weight: bold;
                    color: #2c3e50;
                    spacing: 10px;
                }
                QCheckBox::indicator {
                    width: 20px;
                    height: 20px;
                    border-radius: 3px;
                    border: 2px solid #27ae60;
                }
                QCheckBox::indicator:checked {
                    background-color: #27ae60;
                    border: 2px solid #27ae60;
                }
            """)
            checkbox_layout.addWidget(checkbox)
        
        checkbox_layout.addStretch()
        layout.addLayout(checkbox_layout, 0, 0, 1, 2)
        
        # الاسم والنسب
        ben_name_label = QLabel("الاسم والنسب:")
        ben_name_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.beneficiary_name_edit = QLineEdit()
        self.beneficiary_name_edit.setPlaceholderText("اسم المستفيد من التعويض")
        self.style_input(self.beneficiary_name_edit)
        
        layout.addWidget(ben_name_label, 1, 0)
        layout.addWidget(self.beneficiary_name_edit, 1, 1)
        
        # الهاتف النقال
        phone_label = QLabel("الهاتف النقال:")
        phone_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.mobile_phone_edit = QLineEdit()
        self.mobile_phone_edit.setPlaceholderText("رقم الهاتف النقال")
        self.style_input(self.mobile_phone_edit)
        
        layout.addWidget(phone_label, 2, 0)
        layout.addWidget(self.mobile_phone_edit, 2, 1)
        
        parent_layout.addWidget(group)
        
    def create_action_buttons(self, parent_layout):
        """إنشاء أزرار الإجراءات"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        button_layout.setContentsMargins(0, 20, 0, 20)
        
        # زر الحفظ
        save_button = QPushButton("💾 حفظ الحادثة")
        save_button.setFixedHeight(45)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 25px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        save_button.clicked.connect(self.save_incident)
        
        # زر إعادة تعيين
        reset_button = QPushButton("🔄 إعادة تعيين")
        reset_button.setFixedHeight(45)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 25px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        reset_button.clicked.connect(self.reset_form)
        
        # زر الإلغاء
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(45)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 25px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        cancel_button.clicked.connect(self.cancel_form)
        
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(reset_button)
        button_layout.addWidget(cancel_button)
        button_layout.addStretch()
        
        parent_layout.addLayout(button_layout)
        
    def style_input(self, widget):
        """تنسيق حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_combo(self, widget):
        """تنسيق القوائم المنسدلة"""
        widget.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_date(self, widget):
        """تنسيق حقول التاريخ"""
        widget.setStyleSheet("""
            QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QDateEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_date(self, widget):
        """تنسيق حقول التاريخ"""
        widget.setStyleSheet("""
            QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QDateEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def load_institutions(self, academic_year_id):
        """تحميل المؤسسات للسنة الدراسية المحددة"""
        self.academic_year_id = academic_year_id
        
        # مسح المؤسسات الحالية
        self.institution_combo.clear()
        self.institution_combo.addItem("اختر المؤسسة")
        
        # تحميل المؤسسات
        institutions = self.db_manager.execute_query("""
            SELECT institution_name FROM educational_institutions 
            WHERE academic_year_id = ?
            ORDER BY institution_name
        """, (academic_year_id,), fetch=True)
        
        for institution in institutions:
            self.institution_combo.addItem(institution[0])
            
    def validate_form(self):
        """التحقق من صحة البيانات المدخلة"""
        errors = []
        
        # التحقق من الحقول الإجبارية
        if not self.victim_name_edit.text().strip():
            errors.append("اسم المتضرر مطلوب")
            
        if self.gender_combo.currentIndex() == 0:
            errors.append("نوع المتضرر مطلوب")
            
        if self.institution_combo.currentIndex() == 0:
            errors.append("اسم المؤسسة مطلوب")
            
        if self.environment_combo.currentIndex() == 0:
            errors.append("الوسط مطلوب")
            
        if self.level_combo.currentIndex() == 0:
            errors.append("السلك مطلوب")
            
        if self.status_combo.currentIndex() == 0:
            errors.append("حالة الحادثة مطلوبة")
            
        if self.incident_type_combo.currentIndex() == 0:
            errors.append("نوع الحادثة مطلوب")
            
        if not self.academic_year_id:
            errors.append("يجب اختيار السنة الدراسية")
            
        return errors
        
    def save_incident(self):
        """حفظ الحادثة الجديدة"""
        # التحقق من صحة البيانات
        errors = self.validate_form()
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات", 
                              "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors))
            return
            
        try:
            # الحصول على معرف المؤسسة
            institution_name = self.institution_combo.currentText()
            institution = self.db_manager.execute_query("""
                SELECT id FROM educational_institutions 
                WHERE institution_name = ? AND academic_year_id = ?
            """, (institution_name, self.academic_year_id), fetch=True)
            
            if not institution:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على المؤسسة المحددة")
                return
                
            institution_id = institution[0][0]
            
            # توليد رقم ملف فريد
            file_number = self.generate_file_number()
            
            # إدخال الحادثة
            incident_id = self.db_manager.execute_query("""
                INSERT INTO incidents (
                    file_number, victim_name, student_path_number, gender,
                    institution_id, reference, incident_date, settlement_date,
                    status, incident_type, severity, academic_year_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                file_number,
                self.victim_name_edit.text().strip(),
                self.student_path_edit.text().strip() or None,
                self.gender_combo.currentText(),
                institution_id,
                self.reference_edit.text().strip() or None,
                self.incident_date_edit.date().toString("yyyy-MM-dd"),
                self.settlement_date_edit.date().toString("yyyy-MM-dd"),
                self.status_combo.currentText(),
                self.incident_type_combo.currentText(),
                self.severity_combo.currentText() if self.severity_combo.currentIndex() > 0 else None,
                self.academic_year_id
            ), fetch=False)
            
            # الحصول على معرف الحادثة المُدخلة
            incident_row = self.db_manager.execute_query(
                "SELECT last_insert_rowid()", fetch=True
            )
            
            if incident_row:
                incident_id = incident_row[0][0]
                
                # إدخال بيانات المستفيد إذا كانت متوفرة
                if (self.beneficiary_name_edit.text().strip() or 
                    self.father_checkbox.isChecked() or 
                    self.mother_checkbox.isChecked() or 
                    self.guardian_checkbox.isChecked()):
                    
                    self.db_manager.execute_query("""
                        INSERT INTO beneficiaries (
                            incident_id, is_father, is_mother, is_guardian,
                            full_name, mobile_phone
                        ) VALUES (?, ?, ?, ?, ?, ?)
                    """, (
                        incident_id,
                        1 if self.father_checkbox.isChecked() else 0,
                        1 if self.mother_checkbox.isChecked() else 0,
                        1 if self.guardian_checkbox.isChecked() else 0,
                        self.beneficiary_name_edit.text().strip() or None,
                        self.mobile_phone_edit.text().strip() or None
                    ))
            
            QMessageBox.information(self, "نجح الحفظ", 
                                  f"تم حفظ الحادثة بنجاح\nرقم الملف: {file_number}")
            self.reset_form()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الحادثة:\n{str(e)}")
            
    def generate_file_number(self):
        """توليد رقم ملف فريد"""
        # الحصول على اسم السنة الدراسية
        year = self.db_manager.execute_query("""
            SELECT year_name FROM academic_years WHERE id = ?
        """, (self.academic_year_id,), fetch=True)
        
        year_name = year[0][0] if year else "2024-2025"
        
        # الحصول على آخر رقم ملف للسنة
        last_file = self.db_manager.execute_query("""
            SELECT file_number FROM incidents 
            WHERE academic_year_id = ? 
            ORDER BY id DESC LIMIT 1
        """, (self.academic_year_id,), fetch=True)
        
        if last_file and last_file[0]:
            # استخراج الرقم من آخر ملف
            last_number = last_file[0][0].split('_')[-1]
            try:
                next_number = int(last_number) + 1
            except:
                next_number = 1
        else:
            next_number = 1
            
        return f"FILE_{year_name}_{next_number:03d}"
        
    def reset_form(self):
        """إعادة تعيين النموذج"""
        # مسح حقول النص
        self.victim_name_edit.clear()
        self.student_path_edit.clear()
        self.reference_edit.clear()
        self.beneficiary_name_edit.clear()
        self.mobile_phone_edit.clear()
        
        # إعادة تعيين القوائم المنسدلة
        self.gender_combo.setCurrentIndex(0)
        self.institution_combo.setCurrentIndex(0)
        self.environment_combo.setCurrentIndex(0)
        self.level_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.incident_type_combo.setCurrentIndex(0)
        self.severity_combo.setCurrentIndex(0)
        
        # إعادة تعيين التواريخ
        self.incident_date_edit.setDate(QDate.currentDate())
        self.settlement_date_edit.setDate(QDate.currentDate().addDays(30))
        
        # إعادة تعيين خانات الاختيار
        self.father_checkbox.setChecked(False)
        self.mother_checkbox.setChecked(False)
        self.guardian_checkbox.setChecked(False)
        
    def cancel_form(self):
        """إلغاء النموذج"""
        reply = QMessageBox.question(self, "تأكيد الإلغاء", 
                                   "هل أنت متأكد من إلغاء إدخال الحادثة؟\nسيتم فقدان جميع البيانات المدخلة.",
                                   QMessageBox.Yes | QMessageBox.No, 
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.reset_form()
            
    def get_group_style(self, color):
        """الحصول على ستايل المجموعة"""
        return f"""
            QGroupBox {{
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid {color};
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }}
        """
