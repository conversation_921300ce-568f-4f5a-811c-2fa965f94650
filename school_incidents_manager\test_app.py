#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل للتطبيق مع إصلاح المشاكل
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager
from utils.qt_fixes import setup_qt_environment, restore_stderr

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    
    try:
        # إنشاء مدير قاعدة البيانات
        db_manager = DatabaseManager("test_incidents.db")
        
        # اختبار السنوات الدراسية
        years = db_manager.get_academic_years()
        print(f"✅ تم العثور على {len(years)} سنة دراسية")
        
        # اختبار المؤسسات
        institutions = db_manager.get_institutions()
        print(f"✅ تم العثور على {len(institutions)} مؤسسة تعليمية")
        
        # اختبار الحوادث
        if years:
            year_id = years[0][0]
            incidents = db_manager.get_incidents(year_id)
            print(f"✅ تم العثور على {len(incidents)} حادثة للسنة الدراسية {years[0][1]}")
            
            # اختبار الإحصائيات
            stats = db_manager.get_incident_statistics(year_id)
            print(f"✅ إحصائيات الحوادث: {stats}")
        
        print("✅ جميع اختبارات قاعدة البيانات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_qt_fixes():
    """اختبار إصلاحات Qt"""
    print("🔧 اختبار إصلاحات Qt...")
    
    try:
        # إعداد بيئة Qt
        setup_qt_environment()
        print("✅ تم إعداد بيئة Qt بنجاح")
        
        # اختبار استيراد PySide6
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        print("✅ تم استيراد PySide6 بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Qt: {e}")
        return False

def test_ui_imports():
    """اختبار استيراد ملفات واجهة المستخدم"""
    print("🖥️ اختبار استيراد واجهة المستخدم...")
    
    try:
        from ui.main_window import MainWindow
        print("✅ تم استيراد النافذة الرئيسية")
        
        from ui.dashboard_page import DashboardPage
        print("✅ تم استيراد صفحة لوحة المعلومات")
        
        from ui.add_incident_page import AddIncidentPage
        print("✅ تم استيراد صفحة إضافة الحوادث")
        
        from ui.incidents_list_page import IncidentsListPage
        print("✅ تم استيراد صفحة قائمة الحوادث")
        
        from ui.institutions_page import InstitutionsPage
        print("✅ تم استيراد صفحة المؤسسات")
        
        from ui.reports_page import ReportsPage
        print("✅ تم استيراد صفحة التقارير")
        
        from ui.settings_page import SettingsPage
        print("✅ تم استيراد صفحة الإعدادات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد واجهة المستخدم: {e}")
        return False

def test_arabic_styles():
    """اختبار الأنماط العربية"""
    print("🎨 اختبار الأنماط العربية...")

    try:
        from utils.arabic_styles import get_enhanced_arabic_stylesheet, get_arabic_font

        # اختبار الخط العربي
        font = get_arabic_font()
        print(f"✅ تم تحميل الخط العربي: {font.family()}")

        # اختبار الأنماط
        stylesheet = get_enhanced_arabic_stylesheet()
        if len(stylesheet) > 1000:  # التأكد من وجود محتوى كافي
            print("✅ تم تحميل الأنماط العربية المحسنة")
        else:
            print("⚠️ الأنماط العربية قد تكون ناقصة")

        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الأنماط العربية: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار التطبيق الشامل")
    print("=" * 50)

    tests_passed = 0
    total_tests = 4

    # اختبار قاعدة البيانات
    if test_database_operations():
        tests_passed += 1

    print("-" * 30)

    # اختبار إصلاحات Qt
    if test_qt_fixes():
        tests_passed += 1

    print("-" * 30)

    # اختبار واجهة المستخدم
    if test_ui_imports():
        tests_passed += 1

    print("-" * 30)

    # اختبار الأنماط العربية
    if test_arabic_styles():
        tests_passed += 1

    print("=" * 50)
    print(f"📊 نتائج الاختبار: {tests_passed}/{total_tests} اختبارات نجحت")

    if tests_passed == total_tests:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للتشغيل")
        print("✨ تم إصلاح مشاكل المحاذاة والألوان")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    
    # استعادة stderr الأصلي
    restore_stderr()
    
    sys.exit(0 if success else 1)
