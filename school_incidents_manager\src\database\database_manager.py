import sqlite3
import os
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path="school_incidents.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول السنوات الدراسية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS academic_years (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                year_name TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المؤسسات التعليمية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS educational_institutions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                institution_code TEXT NOT NULL UNIQUE,
                institution_name TEXT NOT NULL,
                community_name TEXT,
                environment TEXT NOT NULL,
                level TEXT NOT NULL,
                institution_type TEXT,
                director_name TEXT,
                phone_number TEXT,
                email TEXT,
                academic_year_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (academic_year_id) REFERENCES academic_years (id)
            )
        ''')
        
        # جدول الحوادث
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS incidents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_number TEXT NOT NULL UNIQUE,
                victim_name TEXT NOT NULL,
                student_path_number TEXT,
                gender TEXT NOT NULL,
                institution_id INTEGER,
                reference TEXT,
                incident_date DATE NOT NULL,
                settlement_date DATE NOT NULL,
                status TEXT NOT NULL,
                incident_type TEXT NOT NULL,
                severity TEXT,
                academic_year_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (institution_id) REFERENCES educational_institutions (id),
                FOREIGN KEY (academic_year_id) REFERENCES academic_years (id)
            )
        ''')
        
        # جدول المستفيدين من التعويض
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS beneficiaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                incident_id INTEGER,
                is_father BOOLEAN DEFAULT 0,
                is_mother BOOLEAN DEFAULT 0,
                is_guardian BOOLEAN DEFAULT 0,
                full_name TEXT,
                mobile_phone TEXT,
                FOREIGN KEY (incident_id) REFERENCES incidents (id) ON DELETE CASCADE
            )
        ''')
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الإعدادات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT NOT NULL UNIQUE,
                setting_value TEXT NOT NULL
            )
        ''')
        
        # إدخال البيانات الافتراضية
        self.insert_default_data(cursor)
        
        conn.commit()
        conn.close()
    
    def insert_default_data(self, cursor):
        """إدخال البيانات الافتراضية"""
        # إضافة السنوات الدراسية الافتراضية
        academic_years = [
            "2018-2019", "2019-2020", "2020-2021", "2021-2022",
            "2022-2023", "2023-2024", "2024-2025", "2025-2026"
        ]
        
        for year in academic_years:
            cursor.execute('''
                INSERT OR IGNORE INTO academic_years (year_name) VALUES (?)
            ''', (year,))
        
        # إضافة مستخدم افتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, role) 
            VALUES (?, ?, ?)
        ''', ("admin", "admin123", "admin"))
        
        # إضافة إعدادات افتراضية
        default_settings = [
            ("font_size", "12"),
            ("current_academic_year", "2024-2025"),
            ("theme", "light")
        ]
        
        for key, value in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO settings (setting_key, setting_value) 
                VALUES (?, ?)
            ''', (key, value))
        
        # إضافة البيانات الافتراضية للمؤسسات والحوادث
        self.insert_sample_data(cursor)
    
    def insert_sample_data(self, cursor):
        """إدخال بيانات افتراضية مؤقتة للمؤسسات والحوادث"""
        
        # الحصول على معرفات السنوات الدراسية
        cursor.execute("SELECT id, year_name FROM academic_years ORDER BY year_name")
        years = cursor.fetchall()
        
        # بيانات المؤسسات التعليمية الافتراضية
        sample_institutions = [
            # مؤسسات ابتدائية
            ("PRIM001", "مدرسة الأمل الابتدائية", "جماعة الرباط", "حضري", "ابتدائي", "عمومية", "أحمد المدير", "0661234567", "<EMAIL>"),
            ("PRIM002", "مدرسة النور الابتدائية", "جماعة سلا", "حضري", "ابتدائي", "عمومية", "فاطمة المديرة", "0662345678", "<EMAIL>"),
            ("PRIM003", "مدرسة القرية الابتدائية", "جماعة تمارة", "قروي", "ابتدائي", "عمومية", "محمد الإدريسي", "0663456789", "<EMAIL>"),
            
            # مؤسسات إعدادية
            ("COLL001", "إعدادية الاستقلال", "جماعة الرباط", "حضري", "إعدادي", "عمومية", "زينب العلوي", "0664567890", "<EMAIL>"),
            ("COLL002", "إعدادية المستقبل", "جماعة سلا", "حضري", "إعدادي", "خصوصية", "عبد الرحمن حسني", "0665678901", "<EMAIL>"),
            ("COLL003", "إعدادية الوحدة", "جماعة الخميسات", "قروي", "إعدادي", "عمومية", "نادية الشريف", "0666789012", "<EMAIL>"),
            
            # مؤسسات تأهيلية
            ("LYC001", "ثانوية محمد الخامس التأهيلية", "جماعة الرباط", "حضري", "تأهيلي", "عمومية", "كريم البركاني", "0667890123", "<EMAIL>"),
            ("LYC002", "ثانوية الحسن الثاني التأهيلية", "جماعة الدار البيضاء", "حضري", "تأهيلي", "عمومية", "سعاد المغربي", "0668901234", "<EMAIL>"),
            ("LYC003", "ثانوية الأندلس التأهيلية", "جماعة فاس", "حضري", "تأهيلي", "خصوصية", "عبد الله الفاسي", "0669012345", "<EMAIL>"),
        ]
        
        # إدخال المؤسسات لكل سنة دراسية
        for year_id, year_name in years:
            for inst_code, inst_name, community, env, level, inst_type, director, phone, email in sample_institutions:
                cursor.execute('''
                    INSERT OR IGNORE INTO educational_institutions 
                    (institution_code, institution_name, community_name, environment, level, 
                     institution_type, director_name, phone_number, email, academic_year_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (f"{inst_code}_{year_name}", inst_name, community, env, level, inst_type, director, phone, email, year_id))
        
        # بيانات الحوادث الافتراضية لكل سنة دراسية
        incidents_by_year = {
            "2018-2019": [
                ("خالد بن أحمد", "18001", "ذكر", "REF2018001", "2018-11-15", "2018-12-15", "تمت التسوية", "حادثة مدرسية", "عادية"),
                ("نورا علي محمد", "18002", "أنثى", "REF2018002", "2018-12-20", "2019-01-20", "في طور التسوية", "حادثة تنقل", "خطيرة"),
                ("عمر سالم الحسن", "18003", "ذكر", "REF2018003", "2019-01-10", "2019-02-10", "تم الدفع", "حادثة رياضية", "عادية"),
                ("مريم عبد الله", "18004", "أنثى", "REF2018004", "2019-02-05", "2019-03-05", "مرفوض", "حادثة مدرسية", "عادية"),
                ("سعد محمد علي", "18005", "ذكر", "REF2018005", "2019-03-25", "2019-04-25", "تمت التسوية", "حادثة تنقل", "خطيرة"),
                ("هند الزهراء", "18006", "أنثى", "REF2018006", "2019-04-12", "2019-05-12", "تم الدفع", "حادثة مدرسية", "عادية"),
                ("إبراهيم حسن", "18007", "ذكر", "REF2018007", "2019-05-08", "2019-06-08", "تمت التسوية", "حادثة رياضية", "خطيرة"),
            ],
            "2019-2020": [
                ("زينب أحمد سالم", "19001", "أنثى", "REF2019001", "2019-10-18", "2019-11-18", "تمت التسوية", "حادثة تنقل", "عادية"),
                ("محمود عبد الرحمن", "19002", "ذكر", "REF2019002", "2019-11-25", "2019-12-25", "في طور التسوية", "حادثة مدرسية", "خطيرة"),
                ("آمنة محمد علي", "19003", "أنثى", "REF2019003", "2020-01-12", "2020-02-12", "تم الدفع", "حادثة رياضية", "عادية"),
                ("طارق سعيد الأمين", "19004", "ذكر", "REF2019004", "2020-02-28", "2020-03-28", "مرفوض", "حادثة تنقل", "عادية"),
                ("ليلى حسام الدين", "19005", "أنثى", "REF2019005", "2020-03-15", "2020-04-15", "تمت التسوية", "حادثة مدرسية", "خطيرة"),
                ("يحيى عبد العزيز", "19006", "ذكر", "REF2019006", "2020-04-20", "2020-05-20", "تم الدفع", "حادثة رياضية", "عادية"),
            ],
            "2020-2021": [
                ("رقية عبد الكريم", "20001", "أنثى", "REF2020001", "2020-09-22", "2020-10-22", "تمت التسوية", "حادثة مدرسية", "عادية"),
                ("كريم محمد الطيب", "20002", "ذكر", "REF2020002", "2020-11-05", "2020-12-05", "في طور التسوية", "حادثة تنقل", "خطيرة"),
                ("سلمى أحمد يوسف", "20003", "أنثى", "REF2020003", "2020-12-18", "2021-01-18", "تم الدفع", "حادثة رياضية", "عادية"),
                ("حمزة عبد الصمد", "20004", "ذكر", "REF2020004", "2021-02-10", "2021-03-10", "مرفوض", "حادثة مدرسية", "عادية"),
                ("خديجة الحسين", "20005", "أنثى", "REF2020005", "2021-03-25", "2021-04-25", "تمت التسوية", "حادثة تنقل", "خطيرة"),
                ("عثمان صلاح", "20006", "ذكر", "REF2020006", "2021-05-12", "2021-06-12", "تم الدفع", "حادثة رياضية", "عادية"),
            ],
            "2021-2022": [
                ("أسماء عبد الغني", "21001", "أنثى", "REF2021001", "2021-10-08", "2021-11-08", "تمت التسوية", "حادثة تنقل", "عادية"),
                ("بلال محمد نور", "21002", "ذكر", "REF2021002", "2021-11-20", "2021-12-20", "في طور التسوية", "حادثة مدرسية", "خطيرة"),
                ("حليمة عبد الرحيم", "21003", "أنثى", "REF2021003", "2022-01-15", "2022-02-15", "تم الدفع", "حادثة رياضية", "عادية"),
                ("إسماعيل الحسن", "21004", "ذكر", "REF2021004", "2022-02-28", "2022-03-28", "مرفوض", "حادثة تنقل", "عادية"),
                ("نجاة محمد البشير", "21005", "أنثى", "REF2021005", "2022-04-10", "2022-05-10", "تمت التسوية", "حادثة مدرسية", "خطيرة"),
                ("منير عبد اللطيف", "21006", "ذكر", "REF2021006", "2022-05-25", "2022-06-25", "تم الدفع", "حادثة رياضية", "عادية"),
            ],
            "2022-2023": [
                ("جميلة أحمد سعيد", "22001", "أنثى", "REF2022001", "2022-09-12", "2022-10-12", "تمت التسوية", "حادثة مدرسية", "عادية"),
                ("عادل محمد الكريم", "22002", "ذكر", "REF2022002", "2022-10-30", "2022-11-30", "في طور التسوية", "حادثة تنقل", "خطيرة"),
                ("وداد عبد الجليل", "22003", "أنثى", "REF2022003", "2022-12-08", "2023-01-08", "تم الدفع", "حادثة رياضية", "عادية"),
                ("رشيد عبد الحميد", "22004", "ذكر", "REF2022004", "2023-01-22", "2023-02-22", "مرفوض", "حادثة مدرسية", "عادية"),
                ("كوثر محمد الأمين", "22005", "أنثى", "REF2022005", "2023-03-18", "2023-04-18", "تمت التسوية", "حادثة تنقل", "خطيرة"),
                ("نبيل عبد الودود", "22006", "ذكر", "REF2022006", "2023-05-05", "2023-06-05", "تم الدفع", "حادثة رياضية", "عادية"),
            ],
            "2023-2024": [
                ("حنان الحسين علي", "23001", "أنثى", "REF2023001", "2023-09-25", "2023-10-25", "تمت التسوية", "حادثة تنقل", "عادية"),
                ("صالح محمد البشير", "23002", "ذكر", "REF2023002", "2023-11-10", "2023-12-10", "في طور التسوية", "حادثة مدرسية", "خطيرة"),
                ("فدوى عبد الكبير", "23003", "أنثى", "REF2023003", "2023-12-28", "2024-01-28", "تم الدفع", "حادثة رياضية", "عادية"),
                ("هشام عبد الستار", "23004", "ذكر", "REF2023004", "2024-02-12", "2024-03-12", "مرفوض", "حادثة تنقل", "عادية"),
                ("ثريا محمد الطاهر", "23005", "أنثى", "REF2023005", "2024-04-08", "2024-05-08", "تمت التسوية", "حادثة مدرسية", "خطيرة"),
                ("جلال عبد الحق", "23006", "ذكر", "REF2023006", "2024-05-20", "2024-06-20", "تم الدفع", "حادثة رياضية", "عادية"),
            ],
            "2024-2025": [
                ("أحمد محمد علي", "24001", "ذكر", "REF2024001", "2024-03-15", "2024-04-15", "تمت التسوية", "حادثة مدرسية", "عادية"),
                ("فاطمة الزهراء أحمد", "24002", "أنثى", "REF2024002", "2024-02-20", "2024-03-20", "في طور التسوية", "حادثة تنقل", "خطيرة"),
                ("محمد عبد الله حسن", "24003", "ذكر", "REF2024003", "2024-01-10", "2024-02-10", "تم الدفع", "حادثة رياضية", "عادية"),
                ("عائشة سعد الدين", "24004", "أنثى", "REF2024004", "2024-04-05", "2024-05-05", "مرفوض", "حادثة مدرسية", "عادية"),
                ("يوسف عبد الرحمن", "24005", "ذكر", "REF2024005", "2024-03-25", "2024-04-25", "تمت التسوية", "حادثة تنقل", "خطيرة"),
                ("زهرة محمد الصديق", "24006", "أنثى", "REF2024006", "2024-05-15", "2024-06-15", "تم الدفع", "حادثة رياضية", "عادية"),
            ],
            "2025-2026": [
                ("ياسين عبد العالي", "25001", "ذكر", "REF2025001", "2025-09-10", "2025-10-10", "في طور التسوية", "حادثة مدرسية", "عادية"),
                ("سارة أحمد الحبيب", "25002", "أنثى", "REF2025002", "2025-10-22", "2025-11-22", "تم الدفع", "حادثة تنقل", "خطيرة"),
                ("معاذ محمد الشريف", "25003", "ذكر", "REF2025003", "2025-11-15", "2025-12-15", "تمت التسوية", "حادثة رياضية", "عادية"),
                ("نوال عبد الجواد", "25004", "أنثى", "REF2025004", "2025-12-05", "2026-01-05", "مرفوض", "حادثة مدرسية", "عادية"),
                ("أنس عبد المجيد", "25005", "ذكر", "REF2025005", "2026-01-20", "2026-02-20", "تمت التسوية", "حادثة تنقل", "خطيرة"),
            ]
        }
        
        # إدخال الحوادث لكل سنة دراسية
        for year_id, year_name in years:
            # الحصول على المؤسسات لهذه السنة
            cursor.execute('''
                SELECT id FROM educational_institutions 
                WHERE academic_year_id = ? 
            ''', (year_id,))
            institutions = cursor.fetchall()
            
            if institutions and year_name in incidents_by_year:
                for i, (victim_name, student_path, gender, ref, incident_date, settlement_date, status, inc_type, severity) in enumerate(incidents_by_year[year_name]):
                    inst_id = institutions[i % len(institutions)][0]
                    file_number = f"FILE_{year_name.replace('-', '_')}_{i+1:03d}"
                    
                    cursor.execute('''
                        INSERT OR IGNORE INTO incidents 
                        (file_number, victim_name, student_path_number, gender, institution_id,
                         reference, incident_date, settlement_date, status, incident_type, 
                         severity, academic_year_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (file_number, victim_name, student_path, gender, inst_id, 
                          ref, incident_date, settlement_date, status, inc_type, severity, year_id))
                    
                    # الحصول على معرف الحادثة المُدخلة
                    cursor.execute("SELECT last_insert_rowid()")
                    incident_id = cursor.fetchone()[0]
                    
                    # إضافة مستفيد للحادثة
                    if incident_id:
                        cursor.execute('''
                            INSERT INTO beneficiaries 
                            (incident_id, is_father, full_name, mobile_phone)
                            VALUES (?, ?, ?, ?)
                        ''', (incident_id, 1, f"والد {victim_name.split()[0]}", f"066{i+1}{year_name[-2:]}567"))

    def execute_query(self, query, params=None, fetch=False):
        """تنفيذ استعلام SQL"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if fetch:
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                lastrowid = cursor.lastrowid
                conn.close()
                return lastrowid
        except Exception as e:
            conn.close()
            raise e
    
    def get_academic_years(self):
        """الحصول على جميع السنوات الدراسية"""
        query = "SELECT id, year_name FROM academic_years ORDER BY year_name DESC"
        return self.execute_query(query, fetch=True)
    
    def add_academic_year(self, year_name):
        """إضافة سنة دراسية جديدة"""
        query = "INSERT INTO academic_years (year_name) VALUES (?)"
        return self.execute_query(query, (year_name,))
    
    def get_institutions(self, academic_year_id=None):
        """الحصول على المؤسسات التعليمية"""
        if academic_year_id:
            query = '''
                SELECT ei.*, ay.year_name 
                FROM educational_institutions ei 
                LEFT JOIN academic_years ay ON ei.academic_year_id = ay.id 
                WHERE ei.academic_year_id = ?
                ORDER BY ei.institution_name
            '''
            return self.execute_query(query, (academic_year_id,), fetch=True)
        else:
            query = '''
                SELECT ei.*, ay.year_name 
                FROM educational_institutions ei 
                LEFT JOIN academic_years ay ON ei.academic_year_id = ay.id 
                ORDER BY ei.institution_name
            '''
            return self.execute_query(query, fetch=True)
    
    def add_institution(self, data):
        """إضافة مؤسسة تعليمية"""
        query = '''
            INSERT INTO educational_institutions 
            (institution_code, institution_name, community_name, environment, 
             level, institution_type, director_name, phone_number, email, academic_year_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        return self.execute_query(query, data)
    
    def get_incidents(self, academic_year_id=None, filters=None):
        """الحصول على الحوادث مع الفلاتر"""
        base_query = '''
            SELECT i.*, ei.institution_name, ei.level, ei.environment, ay.year_name
            FROM incidents i
            LEFT JOIN educational_institutions ei ON i.institution_id = ei.id
            LEFT JOIN academic_years ay ON i.academic_year_id = ay.id
        '''
        
        conditions = []
        params = []
        
        if academic_year_id:
            conditions.append("i.academic_year_id = ?")
            params.append(academic_year_id)
        
        if filters:
            if filters.get('gender'):
                conditions.append("i.gender = ?")
                params.append(filters['gender'])
            if filters.get('environment'):
                conditions.append("ei.environment = ?")
                params.append(filters['environment'])
            if filters.get('level'):
                conditions.append("ei.level = ?")
                params.append(filters['level'])
            if filters.get('incident_type'):
                conditions.append("i.incident_type = ?")
                params.append(filters['incident_type'])
            if filters.get('severity'):
                conditions.append("i.severity = ?")
                params.append(filters['severity'])
            if filters.get('status'):
                conditions.append("i.status = ?")
                params.append(filters['status'])
            if filters.get('date_from'):
                conditions.append("i.incident_date >= ?")
                params.append(filters['date_from'])
            if filters.get('date_to'):
                conditions.append("i.incident_date <= ?")
                params.append(filters['date_to'])
        
        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)
        
        base_query += " ORDER BY i.incident_date DESC"
        
        return self.execute_query(base_query, params, fetch=True)
    
    def add_incident(self, data):
        """إضافة حادثة جديدة"""
        # توليد رقم الملف
        file_number = self.generate_file_number()
        
        query = '''
            INSERT INTO incidents 
            (file_number, victim_name, student_path_number, gender, institution_id, 
             reference, incident_date, settlement_date, status, incident_type, 
             severity, academic_year_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        incident_data = (file_number,) + data
        return self.execute_query(query, incident_data)
    
    def generate_file_number(self):
        """توليد رقم ملف فريد"""
        query = "SELECT COUNT(*) FROM incidents"
        result = self.execute_query(query, fetch=True)
        count = result[0][0] if result else 0
        year = datetime.now().year
        return f"{year}-{count + 1:06d}"
    
    def add_beneficiary(self, incident_id, beneficiary_data):
        """إضافة مستفيد من التعويض"""
        query = '''
            INSERT INTO beneficiaries 
            (incident_id, is_father, is_mother, is_guardian, full_name, mobile_phone)
            VALUES (?, ?, ?, ?, ?, ?)
        '''
        data = (incident_id,) + beneficiary_data
        return self.execute_query(query, data)
    
    def get_incident_statistics(self, academic_year_id):
        """الحصول على إحصائيات الحوادث"""
        stats = {}
        
        # إجمالي الحوادث
        query = "SELECT COUNT(*) FROM incidents WHERE academic_year_id = ?"
        result = self.execute_query(query, (academic_year_id,), fetch=True)
        stats['total'] = result[0][0] if result else 0
        
        # الحوادث حسب الحالة
        statuses = ['في طور التسوية', 'تمت التسوية', 'مرفوض', 'تم الدفع']
        for status in statuses:
            query = "SELECT COUNT(*) FROM incidents WHERE academic_year_id = ? AND status = ?"
            result = self.execute_query(query, (academic_year_id, status), fetch=True)
            stats[status] = result[0][0] if result else 0
        
        return stats
    
    def get_recent_incidents(self, academic_year_id, limit=10):
        """الحصول على آخر الحوادث المسجلة"""
        query = '''
            SELECT i.file_number, i.victim_name, i.incident_date, i.status, ei.institution_name
            FROM incidents i
            LEFT JOIN educational_institutions ei ON i.institution_id = ei.id
            WHERE i.academic_year_id = ?
            ORDER BY i.created_at DESC
            LIMIT ?
        '''
        return self.execute_query(query, (academic_year_id, limit), fetch=True)
    
    def backup_database(self, backup_path):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        import shutil
        try:
            shutil.copy2(self.db_path, backup_path)
            return True
        except Exception:
            return False
    
    def get_setting(self, key):
        """الحصول على قيمة إعداد"""
        query = "SELECT setting_value FROM settings WHERE setting_key = ?"
        result = self.execute_query(query, (key,), fetch=True)
        return result[0][0] if result else None
    
    def update_setting(self, key, value):
        """تحديث قيمة إعداد"""
        query = '''
            INSERT OR REPLACE INTO settings (setting_key, setting_value) 
            VALUES (?, ?)
        '''
        return self.execute_query(query, (key, value))
