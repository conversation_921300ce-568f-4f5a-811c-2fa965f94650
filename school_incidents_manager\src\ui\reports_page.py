# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QTableWidget, QTableWidgetItem,
                             QHeaderView, QFrame, QGridLayout, QGroupBox,
                             QComboBox, QDateEdit, QTextEdit, QScrollArea,
                             QSplitter, QMessageBox, QFileDialog, QProgressBar)
from PySide6.QtCore import Qt, QDate, QThread, Signal
from PySide6.QtGui import QFont, QColor, QPixmap
import sys
import os
from datetime import datetime
import sqlite3

# إضافة مجلد src إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir))
sys.path.append(src_dir)

class ReportsPage(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.academic_year_id = None
        self.setup_ui()
        
    def set_academic_year(self, academic_year_id):
        """تعيين السنة الدراسية الحالية"""
        self.academic_year_id = academic_year_id
        self.generate_statistics()
        
    def refresh_data(self):
        """تحديث البيانات"""
        if self.academic_year_id:
            self.generate_statistics()
        
    def setup_ui(self):
        """إعداد واجهة صفحة التقارير"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("التقارير والإحصائيات")
        title_label.setStyleSheet("""
            font-size: 28px; 
            font-weight: bold; 
            color: #2E86AB; 
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2E86AB;
        """)
        layout.addWidget(title_label)
        
        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(25)
        
        # إنشاء الأقسام المختلفة
        self.create_summary_section(content_layout)
        self.create_detailed_statistics(content_layout)
        self.create_charts_section(content_layout)
        self.create_export_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area, 1)
        
    def create_summary_section(self, parent_layout):
        """إنشاء قسم الملخص السريع"""
        summary_group = QGroupBox("ملخص سريع")
        summary_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        summary_layout = QGridLayout(summary_group)
        summary_layout.setSpacing(20)
        summary_layout.setContentsMargins(20, 30, 20, 20)
        
        # البطاقات الإحصائية
        self.summary_cards = {}
        
        cards_data = [
            ("total_incidents", "إجمالي الحوادث", "0", "#3498db", "📊"),
            ("pending_incidents", "في طور التسوية", "0", "#f39c12", "⏳"),
            ("completed_incidents", "تمت التسوية", "0", "#27ae60", "✅"),
            ("rejected_incidents", "مرفوض", "0", "#e74c3c", "❌"),
            ("paid_incidents", "تم الدفع", "0", "#9b59b6", "💰"),
            ("total_institutions", "المؤسسات", "0", "#16a085", "🏫")
        ]
        
        for i, (key, title, value, color, icon) in enumerate(cards_data):
            card = self.create_summary_card(title, value, color, icon)
            self.summary_cards[key] = card
            row, col = divmod(i, 3)
            summary_layout.addWidget(card, row, col)
            
        parent_layout.addWidget(summary_group)
        
    def create_summary_card(self, title, value, color, icon):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-left: 5px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
            QFrame:hover {{
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }}
        """)
        card.setFixedHeight(120)
        
        layout = QVBoxLayout(card)
        layout.setSpacing(5)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet(f"font-size: 32px; font-weight: bold; color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # حفظ مرجع للقيمة لتحديثها لاحقاً
        card.value_label = value_label
        
        return card
        
    def create_detailed_statistics(self, parent_layout):
        """إنشاء قسم الإحصائيات التفصيلية"""
        details_group = QGroupBox("إحصائيات تفصيلية")
        details_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        details_layout = QVBoxLayout(details_group)
        details_layout.setSpacing(20)
        details_layout.setContentsMargins(20, 30, 20, 20)
        
        # جداول الإحصائيات
        self.create_statistics_tables(details_layout)
        
        parent_layout.addWidget(details_group)
        
    def create_statistics_tables(self, parent_layout):
        """إنشاء جداول الإحصائيات"""
        
        # تقسيم الواجهة إلى جزئين
        splitter = QSplitter(Qt.Horizontal)
        
        # الجدول الأول: إحصائيات حسب السلك
        level_table = self.create_level_statistics_table()
        splitter.addWidget(level_table)
        
        # الجدول الثاني: إحصائيات حسب النوع
        type_table = self.create_type_statistics_table()
        splitter.addWidget(type_table)
        
        splitter.setSizes([300, 300])
        parent_layout.addWidget(splitter)
        
        # الجدول الثالث: إحصائيات حسب الوسط
        env_table = self.create_environment_statistics_table()
        parent_layout.addWidget(env_table)
        
    def create_level_statistics_table(self):
        """إنشاء جدول إحصائيات السلك"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("📈 إحصائيات حسب السلك")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)
        
        self.level_table = QTableWidget()
        self.level_table.setColumnCount(2)
        self.level_table.setHorizontalHeaderLabels(["السلك", "عدد الحوادث"])
        self.level_table.verticalHeader().setVisible(False)
        self.level_table.setAlternatingRowColors(True)
        
        # تنسيق الجدول
        header = self.level_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        
        self.style_table(self.level_table)
        layout.addWidget(self.level_table)
        
        return frame
        
    def create_type_statistics_table(self):
        """إنشاء جدول إحصائيات نوع الحادثة"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("📊 إحصائيات حسب نوع الحادثة")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)
        
        self.type_table = QTableWidget()
        self.type_table.setColumnCount(2)
        self.type_table.setHorizontalHeaderLabels(["نوع الحادثة", "عدد الحوادث"])
        self.type_table.verticalHeader().setVisible(False)
        self.type_table.setAlternatingRowColors(True)
        
        # تنسيق الجدول
        header = self.type_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        
        self.style_table(self.type_table)
        layout.addWidget(self.type_table)
        
        return frame
        
    def create_environment_statistics_table(self):
        """إنشاء جدول إحصائيات الوسط"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #bdc3c7;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("🌍 إحصائيات حسب الوسط")
        title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)
        
        self.environment_table = QTableWidget()
        self.environment_table.setColumnCount(3)
        self.environment_table.setHorizontalHeaderLabels(["الوسط", "عدد الحوادث", "النسبة المئوية"])
        self.environment_table.verticalHeader().setVisible(False)
        self.environment_table.setAlternatingRowColors(True)
        
        # تنسيق الجدول
        header = self.environment_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        
        self.style_table(self.environment_table)
        layout.addWidget(self.environment_table)
        
        return frame
        
    def create_charts_section(self, parent_layout):
        """إنشاء قسم الرسوم البيانية"""
        charts_group = QGroupBox("الرسوم البيانية")
        charts_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        charts_layout = QVBoxLayout(charts_group)
        charts_layout.setContentsMargins(20, 30, 20, 20)
        
        # رسالة قريباً
        charts_label = QLabel("🚧 الرسوم البيانية التفاعلية ستتوفر قريباً")
        charts_label.setStyleSheet("""
            font-size: 18px;
            color: #7f8c8d;
            padding: 50px;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #bdc3c7;
        """)
        charts_label.setAlignment(Qt.AlignCenter)
        charts_layout.addWidget(charts_label)
        
        parent_layout.addWidget(charts_group)
        
    def create_export_section(self, parent_layout):
        """إنشاء قسم التصدير"""
        export_group = QGroupBox("تصدير التقارير")
        export_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #9b59b6;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        export_layout = QHBoxLayout(export_group)
        export_layout.setContentsMargins(20, 30, 20, 20)
        export_layout.setSpacing(15)
        
        # أزرار التصدير
        export_buttons = [
            ("📄 تصدير PDF", "#e74c3c", self.export_pdf),
            ("📊 تصدير Excel", "#27ae60", self.export_excel),
            ("🖨️ طباعة", "#3498db", self.print_report)
        ]
        
        for text, color, callback in export_buttons:
            btn = QPushButton(text)
            btn.setFixedHeight(50)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 0 30px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
            """)
            btn.clicked.connect(callback)
            export_layout.addWidget(btn)
            
        export_layout.addStretch()
        
        parent_layout.addWidget(export_group)
        
    def style_table(self, table):
        """تنسيق الجداول"""
        table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                color: #2c3e50;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
        """)
        table.setFixedHeight(200)
        
    def generate_statistics(self):
        """توليد الإحصائيات"""
        if not self.academic_year_id:
            return
            
        try:
            # إحصائيات عامة
            self.update_summary_statistics()
            
            # إحصائيات تفصيلية
            self.update_level_statistics()
            self.update_type_statistics()
            self.update_environment_statistics()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء توليد الإحصائيات:\n{str(e)}")
            
    def update_summary_statistics(self):
        """تحديث الإحصائيات السريعة"""
        
        # إحصائيات الحوادث
        incidents_query = """
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'في طور التسوية' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'تمت التسوية' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'مرفوض' THEN 1 ELSE 0 END) as rejected,
                SUM(CASE WHEN status = 'تم الدفع' THEN 1 ELSE 0 END) as paid
            FROM incidents 
            WHERE academic_year_id = ?
        """
        
        stats = self.db_manager.execute_query(incidents_query, (self.academic_year_id,), fetch=True)
        
        if stats and stats[0]:
            total, pending, completed, rejected, paid = stats[0]
            
            self.summary_cards["total_incidents"].value_label.setText(str(total or 0))
            self.summary_cards["pending_incidents"].value_label.setText(str(pending or 0))
            self.summary_cards["completed_incidents"].value_label.setText(str(completed or 0))
            self.summary_cards["rejected_incidents"].value_label.setText(str(rejected or 0))
            self.summary_cards["paid_incidents"].value_label.setText(str(paid or 0))
        
        # إحصائيات المؤسسات
        institutions_query = """
            SELECT COUNT(*) FROM educational_institutions 
            WHERE academic_year_id = ?
        """
        
        inst_stats = self.db_manager.execute_query(institutions_query, (self.academic_year_id,), fetch=True)
        
        if inst_stats and inst_stats[0]:
            self.summary_cards["total_institutions"].value_label.setText(str(inst_stats[0][0] or 0))
            
    def update_level_statistics(self):
        """تحديث إحصائيات السلك"""
        query = """
            SELECT ei.level, COUNT(i.id) as count
            FROM incidents i
            JOIN educational_institutions ei ON i.institution_id = ei.id
            WHERE i.academic_year_id = ?
            GROUP BY ei.level
            ORDER BY count DESC
        """
        
        stats = self.db_manager.execute_query(query, (self.academic_year_id,), fetch=True)
        
        self.level_table.setRowCount(len(stats) if stats else 0)
        
        if stats:
            for row, (level, count) in enumerate(stats):
                level_item = QTableWidgetItem(str(level))
                level_item.setForeground(QColor("#2c3e50"))
                self.level_table.setItem(row, 0, level_item)
                
                count_item = QTableWidgetItem(str(count))
                count_item.setTextAlignment(Qt.AlignCenter)
                count_item.setForeground(QColor("#2c3e50"))
                self.level_table.setItem(row, 1, count_item)
                
    def update_type_statistics(self):
        """تحديث إحصائيات نوع الحادثة"""
        query = """
            SELECT incident_type, COUNT(*) as count
            FROM incidents
            WHERE academic_year_id = ?
            GROUP BY incident_type
            ORDER BY count DESC
        """
        
        stats = self.db_manager.execute_query(query, (self.academic_year_id,), fetch=True)
        
        self.type_table.setRowCount(len(stats) if stats else 0)
        
        if stats:
            for row, (inc_type, count) in enumerate(stats):
                type_item = QTableWidgetItem(str(inc_type))
                type_item.setForeground(QColor("#2c3e50"))
                self.type_table.setItem(row, 0, type_item)
                
                count_item = QTableWidgetItem(str(count))
                count_item.setTextAlignment(Qt.AlignCenter)
                count_item.setForeground(QColor("#2c3e50"))
                self.type_table.setItem(row, 1, count_item)
                
    def update_environment_statistics(self):
        """تحديث إحصائيات الوسط"""
        query = """
            SELECT ei.environment, COUNT(i.id) as count
            FROM incidents i
            JOIN educational_institutions ei ON i.institution_id = ei.id
            WHERE i.academic_year_id = ?
            GROUP BY ei.environment
            ORDER BY count DESC
        """
        
        stats = self.db_manager.execute_query(query, (self.academic_year_id,), fetch=True)
        
        total_incidents = sum(count for _, count in stats) if stats else 0
        
        self.environment_table.setRowCount(len(stats) if stats else 0)
        
        if stats:
            for row, (environment, count) in enumerate(stats):
                env_item = QTableWidgetItem(str(environment))
                env_item.setForeground(QColor("#2c3e50"))
                self.environment_table.setItem(row, 0, env_item)
                
                count_item = QTableWidgetItem(str(count))
                count_item.setTextAlignment(Qt.AlignCenter)
                count_item.setForeground(QColor("#2c3e50"))
                self.environment_table.setItem(row, 1, count_item)
                
                # حساب النسبة المئوية
                percentage = (count / total_incidents * 100) if total_incidents > 0 else 0
                percentage_item = QTableWidgetItem(f"{percentage:.1f}%")
                percentage_item.setTextAlignment(Qt.AlignCenter)
                percentage_item.setForeground(QColor("#2c3e50"))
                self.environment_table.setItem(row, 2, percentage_item)
                
    def darken_color(self, color):
        """تغميق اللون للتأثير hover"""
        color_map = {
            "#e74c3c": "#c0392b",
            "#27ae60": "#229954",
            "#3498db": "#2980b9",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad"
        }
        return color_map.get(color, color)
        
    def export_pdf(self):
        """تصدير التقرير كـ PDF"""
        QMessageBox.information(self, "قريباً", "ميزة تصدير PDF ستتوفر قريباً")
        
    def export_excel(self):
        """تصدير التقرير كـ Excel"""
        QMessageBox.information(self, "قريباً", "ميزة تصدير Excel ستتوفر قريباً")
        
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "ميزة الطباعة ستتوفر قريباً")
