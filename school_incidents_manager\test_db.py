#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة مسار src إلى Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager

def test_database():
    """اختبار إنشاء قاعدة البيانات مع البيانات الافتراضية"""
    
    # إنشاء مدير قاعدة البيانات
    db_manager = DatabaseManager("school_incidents_test.db")
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    
    # اختبار البيانات المُدخلة
    
    # عرض السنوات الدراسية
    years = db_manager.execute_query("SELECT * FROM academic_years", fetch=True)
    print(f"📅 تم إنشاء {len(years)} سنة دراسية:")
    for year in years:
        print(f"   - {year[1]}")
    
    # عرض المؤسسات التعليمية
    institutions = db_manager.execute_query("SELECT COUNT(*) FROM educational_institutions", fetch=True)
    print(f"🏫 تم إنشاء {institutions[0][0]} مؤسسة تعليمية")
    
    # عرض الحوادث
    incidents = db_manager.execute_query("SELECT COUNT(*) FROM incidents", fetch=True)
    print(f"📋 تم إنشاء {incidents[0][0]} حادثة")
    
    # عرض المستفيدين
    beneficiaries = db_manager.execute_query("SELECT COUNT(*) FROM beneficiaries", fetch=True)
    print(f"👥 تم إنشاء {beneficiaries[0][0]} مستفيد")
    
    # عرض عينة من الحوادث
    sample_incidents = db_manager.execute_query("""
        SELECT i.file_number, i.victim_name, ei.institution_name, ay.year_name, i.status
        FROM incidents i
        JOIN educational_institutions ei ON i.institution_id = ei.id
        JOIN academic_years ay ON i.academic_year_id = ay.id
        LIMIT 10
    """, fetch=True)
    
    print("\n📊 عينة من الحوادث المُدخلة:")
    for incident in sample_incidents:
        print(f"   📁 {incident[0]} - {incident[1]} - {incident[2]} - {incident[3]} - {incident[4]}")
    
    print("\n✅ تم اختبار قاعدة البيانات بنجاح!")
    print("💡 هذه بيانات افتراضية مؤقتة لأغراض الاختبار")

if __name__ == "__main__":
    test_database()
