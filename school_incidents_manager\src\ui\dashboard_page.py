from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QFrame, QGridLayout, QTableWidget, QTableWidgetItem,
                            QHeaderView, QScrollArea)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QColor
import sys
import os

# إضافة مجلد src إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir))
sys.path.append(src_dir)

class DashboardPage(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.academic_year_id = None
        self.setup_ui()
        
    def set_academic_year(self, academic_year_id):
        """تعيين السنة الدراسية الحالية"""
        self.academic_year_id = academic_year_id
        self.refresh_data()
        
    def refresh_data(self):
        """تحديث البيانات"""
        if self.academic_year_id:
            self.update_dashboard(self.academic_year_id)
        
    def setup_ui(self):
        """إعداد واجهة لوحة المعلومات"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("لوحة المعلومات")
        title_label.setObjectName("page_title")
        title_label.setStyleSheet("font-size: 24pt; font-weight: bold; color: #2E86AB; margin-bottom: 20px;")
        title_label.setAlignment(Qt.AlignRight)
        layout.addWidget(title_label)
        
        # منطقة الإحصائيات
        self.create_statistics_section(layout)
        
        # منطقة آخر الحوادث
        self.create_recent_incidents_section(layout)
        
        layout.addStretch()
        
    def create_statistics_section(self, parent_layout):
        """إنشاء قسم الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("background-color: white; border-radius: 10px; padding: 20px;")
        stats_layout = QVBoxLayout(stats_frame)
        
        # عنوان القسم
        stats_title = QLabel("إحصائيات الحوادث")
        stats_title.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2E86AB; margin-bottom: 15px;")
        stats_title.setAlignment(Qt.AlignRight)
        stats_layout.addWidget(stats_title)
        
        # شبكة البطاقات الإحصائية
        stats_grid = QGridLayout()
        stats_grid.setSpacing(15)
        
        # إنشاء البطاقات الإحصائية
        self.total_card = self.create_stat_card("إجمالي الحوادث", "0", "#2E86AB")
        self.pending_card = self.create_stat_card("في طور التسوية", "0", "#F9844A")
        self.completed_card = self.create_stat_card("تمت التسوية", "0", "#43AA8B")
        self.rejected_card = self.create_stat_card("مرفوض", "0", "#F8333C")
        
        stats_grid.addWidget(self.total_card, 0, 0)
        stats_grid.addWidget(self.pending_card, 0, 1)
        stats_grid.addWidget(self.completed_card, 0, 2)
        stats_grid.addWidget(self.rejected_card, 0, 3)
        
        stats_layout.addLayout(stats_grid)
        parent_layout.addWidget(stats_frame)
        
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFixedHeight(120)
        card.setStyleSheet(f"""
            QFrame {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 {color}, stop: 1 {self.darken_color(color)});
                border-radius: 12px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("color: white; font-size: 28pt; font-weight: bold; margin-bottom: 5px;")
        layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: white; font-size: 12pt; font-weight: bold;")
        layout.addWidget(title_label)
        
        # حفظ مرجع لقيمة البطاقة لتحديثها لاحقاً
        card.value_label = value_label
        
        return card
    
    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            "#2E86AB": "#1e5f82",
            "#F9844A": "#e6733a",
            "#43AA8B": "#369970",
            "#F8333C": "#e01e28"
        }
        return color_map.get(color, color)
        
    def create_recent_incidents_section(self, parent_layout):
        """إنشاء قسم آخر الحوادث"""
        recent_frame = QFrame()
        recent_frame.setStyleSheet("background-color: white; border-radius: 10px; padding: 20px;")
        recent_layout = QVBoxLayout(recent_frame)
        
        # عنوان القسم
        recent_title = QLabel("آخر الحوادث المسجلة")
        recent_title.setStyleSheet("font-size: 18pt; font-weight: bold; color: #2E86AB; margin-bottom: 15px;")
        recent_title.setAlignment(Qt.AlignRight)
        recent_layout.addWidget(recent_title)
        
        # جدول آخر الحوادث
        self.recent_incidents_table = QTableWidget()
        self.recent_incidents_table.setColumnCount(5)
        self.recent_incidents_table.setHorizontalHeaderLabels([
            "رقم الملف", "اسم المتضرر", "تاريخ الحادثة", "الحالة", "المؤسسة"
        ])
        
        # إعداد الجدول
        header = self.recent_incidents_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.recent_incidents_table.setAlternatingRowColors(True)
        self.recent_incidents_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.recent_incidents_table.setFixedHeight(300)
        
        # تنسيق الجدول
        self.recent_incidents_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                gridline-color: #dee2e6;
                selection-background-color: #2E86AB;
                selection-color: white;
                color: #212529;
            }
            QTableWidget::item {
                padding: 10px;
                text-align: right;
                color: #212529;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #2E86AB;
                color: white;
            }
            QTableWidget::item:alternate {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                color: #495057;
                padding: 12px;
                border: none;
                font-weight: bold;
                text-align: right;
            }
        """)
        
        recent_layout.addWidget(self.recent_incidents_table)
        parent_layout.addWidget(recent_frame)
        

        
    def refresh_data(self):
        """تحديث البيانات"""
        if self.academic_year_id:
            self.update_statistics()
            self.update_recent_incidents()
            
    def update_statistics(self):
        """تحديث الإحصائيات"""
        if not self.academic_year_id:
            return
            
        stats = self.db_manager.get_incident_statistics(self.academic_year_id)
        
        # تحديث البطاقات
        self.total_card.value_label.setText(str(stats.get('total', 0)))
        self.pending_card.value_label.setText(str(stats.get('في طور التسوية', 0)))
        self.completed_card.value_label.setText(str(stats.get('تمت التسوية', 0)))
        self.rejected_card.value_label.setText(str(stats.get('مرفوض', 0)))
        
    def update_recent_incidents(self):
        """تحديث آخر الحوادث"""
        if not self.academic_year_id:
            return
            
        recent_incidents = self.db_manager.get_recent_incidents(self.academic_year_id, 10)
        
        # تعيين عدد الصفوف
        self.recent_incidents_table.setRowCount(len(recent_incidents))
        
        # ملء الجدول
        for row, incident in enumerate(recent_incidents):
            file_number, victim_name, incident_date, status, institution_name = incident
            
            # رقم الملف
            file_item = QTableWidgetItem(str(file_number or ""))
            file_item.setForeground(QColor("#212529"))
            file_item.setBackground(QColor("#ffffff"))
            file_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.recent_incidents_table.setItem(row, 0, file_item)

            # اسم المتضرر
            name_item = QTableWidgetItem(str(victim_name or ""))
            name_item.setForeground(QColor("#212529"))
            name_item.setBackground(QColor("#ffffff"))
            name_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.recent_incidents_table.setItem(row, 1, name_item)

            # تاريخ الحادثة
            date_item = QTableWidgetItem(str(incident_date or ""))
            date_item.setForeground(QColor("#212529"))
            date_item.setBackground(QColor("#ffffff"))
            date_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.recent_incidents_table.setItem(row, 2, date_item)
            
            # تلوين الحالة
            status_item = QTableWidgetItem(str(status or ""))
            status_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            if status == "تمت التسوية":
                status_item.setBackground(QColor("#43AA8B"))
                status_item.setForeground(QColor("#ffffff"))
            elif status == "في طور التسوية":
                status_item.setBackground(QColor("#F9844A"))
                status_item.setForeground(QColor("#ffffff"))
            elif status == "مرفوض":
                status_item.setBackground(QColor("#F8333C"))
                status_item.setForeground(QColor("#ffffff"))
            elif status == "تم الدفع":
                status_item.setBackground(QColor("#2E86AB"))
                status_item.setForeground(QColor("#ffffff"))
            else:
                status_item.setBackground(QColor("#6c757d"))
                status_item.setForeground(QColor("#ffffff"))

            self.recent_incidents_table.setItem(row, 3, status_item)

            # اسم المؤسسة
            institution_item = QTableWidgetItem(str(institution_name or ""))
            institution_item.setForeground(QColor("#212529"))
            institution_item.setBackground(QColor("#ffffff"))
            institution_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.recent_incidents_table.setItem(row, 4, institution_item)

