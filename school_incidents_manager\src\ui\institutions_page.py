# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QComboBox, QPushButton, QTableWidget,
                             QTableWidgetItem, QHeaderView, QFrame, QGridLayout,
                             QGroupBox, QMessageBox, QDialog, QTextEdit,
                             QScrollArea, QSplitter)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QColor
import sys
import os

# إضافة مجلد src إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir))
sys.path.append(src_dir)

class AddInstitutionDialog(QDialog):
    """نافذة إضافة مؤسسة تعليمية جديدة"""
    def __init__(self, db_manager, academic_year_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.academic_year_id = academic_year_id
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة إضافة المؤسسة"""
        self.setWindowTitle("إضافة مؤسسة تعليمية جديدة")
        self.setMinimumSize(600, 650)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان النافذة
        title_label = QLabel("إضافة مؤسسة تعليمية جديدة")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #2E86AB;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2E86AB;
        """)
        layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        content_widget = QWidget()
        content_layout = QGridLayout(content_widget)
        content_layout.setSpacing(15)
        
        # الحقول المطلوبة
        # اسم المؤسسة (إجباري)
        name_label = QLabel("اسم المؤسسة *:")
        name_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المؤسسة")
        self.style_input(self.name_edit)
        
        content_layout.addWidget(name_label, 0, 0)
        content_layout.addWidget(self.name_edit, 0, 1)
        
        # رمز المؤسسة (إجباري)
        code_label = QLabel("رمز المؤسسة *:")
        code_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("أدخل رمز المؤسسة (مثل: PRIM001)")
        self.style_input(self.code_edit)
        
        content_layout.addWidget(code_label, 1, 0)
        content_layout.addWidget(self.code_edit, 1, 1)
        
        # اسم الجماعة
        community_label = QLabel("اسم الجماعة:")
        community_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.community_edit = QLineEdit()
        self.community_edit.setPlaceholderText("أدخل اسم الجماعة")
        self.style_input(self.community_edit)
        
        content_layout.addWidget(community_label, 2, 0)
        content_layout.addWidget(self.community_edit, 2, 1)
        
        # الوسط (إجباري)
        env_label = QLabel("الوسط *:")
        env_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.environment_combo = QComboBox()
        self.environment_combo.addItems(["اختر الوسط", "قروي", "حضري"])
        self.style_combo(self.environment_combo)
        
        content_layout.addWidget(env_label, 3, 0)
        content_layout.addWidget(self.environment_combo, 3, 1)
        
        # السلك (إجباري)
        level_label = QLabel("السلك *:")
        level_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.level_combo = QComboBox()
        self.level_combo.addItems(["اختر السلك", "ابتدائي", "إعدادي", "تأهيلي"])
        self.style_combo(self.level_combo)
        
        content_layout.addWidget(level_label, 4, 0)
        content_layout.addWidget(self.level_combo, 4, 1)
        
        # طبيعة المؤسسة
        type_label = QLabel("طبيعة المؤسسة:")
        type_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.institution_type_combo = QComboBox()
        self.institution_type_combo.addItems(["عمومية", "خصوصية"])
        self.style_combo(self.institution_type_combo)
        
        content_layout.addWidget(type_label, 5, 0)
        content_layout.addWidget(self.institution_type_combo, 5, 1)
        
        # اسم المدير
        director_label = QLabel("اسم المدير:")
        director_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.director_edit = QLineEdit()
        self.director_edit.setPlaceholderText("أدخل اسم المدير")
        self.style_input(self.director_edit)
        
        content_layout.addWidget(director_label, 6, 0)
        content_layout.addWidget(self.director_edit, 6, 1)
        
        # رقم الهاتف
        phone_label = QLabel("رقم الهاتف:")
        phone_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("أدخل رقم الهاتف")
        self.style_input(self.phone_edit)
        
        content_layout.addWidget(phone_label, 7, 0)
        content_layout.addWidget(self.phone_edit, 7, 1)
        
        # البريد الإلكتروني
        email_label = QLabel("البريد الإلكتروني:")
        email_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني")
        self.style_input(self.email_edit)
        
        content_layout.addWidget(email_label, 8, 0)
        content_layout.addWidget(self.email_edit, 8, 1)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area, 1)
        
        # أزرار الإجراءات
        button_layout = QHBoxLayout()
        
        save_button = QPushButton("💾 حفظ")
        save_button.setFixedHeight(40)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_button.clicked.connect(self.save_institution)
        
        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setFixedHeight(40)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
        
    def style_input(self, widget):
        """تنسيق حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_combo(self, widget):
        """تنسيق القوائم المنسدلة"""
        widget.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #2c3e50;
                width: 0px;
                height: 0px;
            }
        """)
        widget.setFixedHeight(35)
        
    def validate_form(self):
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.name_edit.text().strip():
            errors.append("اسم المؤسسة مطلوب")
            
        if not self.code_edit.text().strip():
            errors.append("رمز المؤسسة مطلوب")
            
        if self.environment_combo.currentIndex() == 0:
            errors.append("الوسط مطلوب")
            
        if self.level_combo.currentIndex() == 0:
            errors.append("السلك مطلوب")
            
        # التحقق من عدم تكرار رمز المؤسسة
        if self.code_edit.text().strip():
            existing = self.db_manager.execute_query("""
                SELECT id FROM educational_institutions 
                WHERE institution_code = ? AND academic_year_id = ?
            """, (self.code_edit.text().strip(), self.academic_year_id), fetch=True)
            
            if existing:
                errors.append("رمز المؤسسة موجود مسبقاً لهذه السنة الدراسية")
                
        return errors
        
    def save_institution(self):
        """حفظ المؤسسة الجديدة"""
        errors = self.validate_form()
        if errors:
            QMessageBox.warning(self, "خطأ في البيانات",
                              "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(f"• {error}" for error in errors))
            return
            
        try:
            self.db_manager.execute_query("""
                INSERT INTO educational_institutions (
                    institution_code, institution_name, community_name, 
                    environment, level, institution_type, director_name,
                    phone_number, email, academic_year_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.code_edit.text().strip(),
                self.name_edit.text().strip(),
                self.community_edit.text().strip() or None,
                self.environment_combo.currentText(),
                self.level_combo.currentText(),
                self.institution_type_combo.currentText(),
                self.director_edit.text().strip() or None,
                self.phone_edit.text().strip() or None,
                self.email_edit.text().strip() or None,
                self.academic_year_id
            ))
            
            QMessageBox.information(self, "نجح الحفظ", "تم حفظ المؤسسة بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المؤسسة:\n{str(e)}")

class InstitutionsPage(QWidget):
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.academic_year_id = None
        self.setup_ui()
        
    def set_academic_year(self, academic_year_id):
        """تعيين السنة الدراسية الحالية"""
        self.academic_year_id = academic_year_id
        self.load_institutions(academic_year_id)
        
    def refresh_data(self):
        """تحديث البيانات"""
        if self.academic_year_id:
            self.load_institutions(self.academic_year_id)
        
    def setup_ui(self):
        """إعداد واجهة صفحة المؤسسات"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("إدارة المؤسسات التعليمية")
        title_label.setStyleSheet("""
            font-size: 28px; 
            font-weight: bold; 
            color: #2E86AB; 
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #2E86AB;
        """)
        layout.addWidget(title_label)
        
        # إنشاء قسم البحث والتصفية
        self.create_filter_section(layout)
        
        # إنشاء قسم الإجراءات
        self.create_actions_section(layout)
        
        # إنشاء جدول المؤسسات
        self.create_institutions_table(layout)
        
    def create_filter_section(self, parent_layout):
        """إنشاء قسم البحث والتصفية"""
        filter_group = QGroupBox("البحث والتصفية")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }
        """)
        
        filter_layout = QGridLayout(filter_group)
        filter_layout.setSpacing(15)
        filter_layout.setContentsMargins(20, 30, 20, 20)
        
        # البحث بالاسم
        search_label = QLabel("البحث بالاسم:")
        search_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم أو رمز المؤسسة...")
        self.style_input(self.search_edit)
        self.search_edit.textChanged.connect(self.filter_institutions)
        
        filter_layout.addWidget(search_label, 0, 0)
        filter_layout.addWidget(self.search_edit, 0, 1)
        
        # تصفية حسب السلك
        level_label = QLabel("السلك:")
        level_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.level_filter = QComboBox()
        self.level_filter.addItems(["الكل", "ابتدائي", "إعدادي", "تأهيلي"])
        self.style_combo(self.level_filter)
        self.level_filter.currentTextChanged.connect(self.filter_institutions)
        
        filter_layout.addWidget(level_label, 0, 2)
        filter_layout.addWidget(self.level_filter, 0, 3)
        
        # تصفية حسب الجماعة
        community_label = QLabel("الجماعة:")
        community_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.community_filter = QComboBox()
        self.community_filter.addItem("الكل")
        self.style_combo(self.community_filter)
        self.community_filter.currentTextChanged.connect(self.filter_institutions)
        
        filter_layout.addWidget(community_label, 1, 0)
        filter_layout.addWidget(self.community_filter, 1, 1)
        
        # تصفية حسب الوسط
        env_label = QLabel("الوسط:")
        env_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.environment_filter = QComboBox()
        self.environment_filter.addItems(["الكل", "قروي", "حضري"])
        self.style_combo(self.environment_filter)
        self.environment_filter.currentTextChanged.connect(self.filter_institutions)
        
        filter_layout.addWidget(env_label, 1, 2)
        filter_layout.addWidget(self.environment_filter, 1, 3)
        
        # تصفية حسب طبيعة المؤسسة
        type_label = QLabel("طبيعة المؤسسة:")
        type_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        self.type_filter = QComboBox()
        self.type_filter.addItems(["الكل", "عمومية", "خصوصية"])
        self.style_combo(self.type_filter)
        self.type_filter.currentTextChanged.connect(self.filter_institutions)
        
        filter_layout.addWidget(type_label, 2, 0)
        filter_layout.addWidget(self.type_filter, 2, 1)
        
        # أزرار الإجراءات
        button_layout = QHBoxLayout()
        
        reset_button = QPushButton("🔄 إعادة تعيين")
        reset_button.setFixedHeight(40)
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 20px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        reset_button.clicked.connect(self.reset_filters)
        
        refresh_button = QPushButton("🔃 تحديث")
        refresh_button.setFixedHeight(40)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 0 20px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        refresh_button.clicked.connect(self.load_institutions)
        
        button_layout.addStretch()
        button_layout.addWidget(reset_button)
        button_layout.addWidget(refresh_button)
        
        filter_layout.addLayout(button_layout, 2, 2, 1, 2)
        
        parent_layout.addWidget(filter_group)
        
    def create_actions_section(self, parent_layout):
        """إنشاء قسم الإجراءات"""
        actions_layout = QHBoxLayout()
        
        # زر إضافة مؤسسة
        add_button = QPushButton("➕ إضافة مؤسسة تعليمية")
        add_button.setFixedHeight(50)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                padding: 0 30px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        add_button.clicked.connect(self.add_institution)
        
        # إحصائيات سريعة
        self.stats_label = QLabel("إجمالي المؤسسات: 0")
        self.stats_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px 20px;
            background-color: #ecf0f1;
            border-radius: 8px;
        """)
        
        actions_layout.addWidget(add_button)
        actions_layout.addStretch()
        actions_layout.addWidget(self.stats_label)
        
        parent_layout.addLayout(actions_layout)
        
    def create_institutions_table(self, parent_layout):
        """إنشاء جدول المؤسسات"""
        self.institutions_table = QTableWidget()
        self.institutions_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
                color: #2c3e50;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        # أعمدة الجدول
        headers = [
            "رمز المؤسسة", "اسم المؤسسة", "الجماعة", "الوسط", 
            "السلك", "النوع", "المدير", "الهاتف", "الإجراءات"
        ]
        
        self.institutions_table.setColumnCount(len(headers))
        self.institutions_table.setHorizontalHeaderLabels(headers)
        
        # إعداد الجدول
        self.institutions_table.setAlternatingRowColors(True)
        self.institutions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.institutions_table.verticalHeader().setVisible(False)
        
        # توزيع عرض الأعمدة
        header = self.institutions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رمز المؤسسة
        header.setSectionResizeMode(1, QHeaderView.Stretch)           # اسم المؤسسة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # الجماعة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الوسط
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # السلك
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # النوع
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # المدير
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # الإجراءات
        
        parent_layout.addWidget(self.institutions_table, 1)
        
    def style_input(self, widget):
        """تنسيق حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        widget.setFixedHeight(35)
        
    def style_combo(self, widget):
        """تنسيق القوائم المنسدلة"""
        widget.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #2c3e50;
                width: 0px;
                height: 0px;
            }
        """)
        widget.setFixedHeight(35)
        
    def load_institutions(self, academic_year_id=None):
        """تحميل المؤسسات للسنة الدراسية المحددة"""
        if academic_year_id:
            self.academic_year_id = academic_year_id
            
        if not self.academic_year_id:
            return
            
        # تحميل المؤسسات
        query = """
            SELECT 
                institution_code, institution_name, community_name, 
                environment, level, institution_type, director_name, 
                phone_number, email, id
            FROM educational_institutions 
            WHERE academic_year_id = ?
            ORDER BY level, institution_name
        """
        
        self.all_institutions = self.db_manager.execute_query(query, (self.academic_year_id,), fetch=True)
        
        # تحديث قائمة الجماعات
        self.update_communities_filter()
        
        # عرض المؤسسات
        self.filter_institutions()
        
    def update_communities_filter(self):
        """تحديث قائمة الجماعات في التصفية"""
        self.community_filter.clear()
        self.community_filter.addItem("الكل")
        
        if hasattr(self, 'all_institutions') and self.all_institutions:
            communities = set()
            for institution in self.all_institutions:
                if institution[2]:  # community_name
                    communities.add(institution[2])
            
            for community in sorted(communities):
                self.community_filter.addItem(community)
                
    def filter_institutions(self):
        """تطبيق التصفية على المؤسسات"""
        if not hasattr(self, 'all_institutions') or not self.all_institutions:
            return
            
        filtered_institutions = []
        
        search_text = self.search_edit.text().lower()
        level_filter = self.level_filter.currentText()
        community_filter = self.community_filter.currentText()
        env_filter = self.environment_filter.currentText()
        type_filter = self.type_filter.currentText()
        
        for institution in self.all_institutions:
            # البحث بالاسم أو الرمز
            if search_text and (search_text not in institution[1].lower() and 
                               search_text not in institution[0].lower()):
                continue
                
            # تصفية السلك
            if level_filter != "الكل" and institution[4] != level_filter:
                continue
                
            # تصفية الجماعة
            if community_filter != "الكل" and institution[2] != community_filter:
                continue
                
            # تصفية الوسط
            if env_filter != "الكل" and institution[3] != env_filter:
                continue
                
            # تصفية النوع
            if type_filter != "الكل" and institution[5] != type_filter:
                continue
                
            filtered_institutions.append(institution)
            
        self.display_institutions(filtered_institutions)
        
    def display_institutions(self, institutions):
        """عرض المؤسسات في الجدول"""
        self.institutions_table.setRowCount(len(institutions))
        
        for row, institution in enumerate(institutions):
            # رمز المؤسسة
            id_item = QTableWidgetItem(str(institution[0]))
            id_item.setForeground(QColor("#2c3e50"))
            self.institutions_table.setItem(row, 0, id_item)
            
            # اسم المؤسسة
            name_item = QTableWidgetItem(str(institution[1]))
            name_item.setForeground(QColor("#2c3e50"))
            self.institutions_table.setItem(row, 1, name_item)
            
            # الجماعة
            commune_item = QTableWidgetItem(str(institution[2] or ""))
            commune_item.setForeground(QColor("#2c3e50"))
            self.institutions_table.setItem(row, 2, commune_item)
            
            # الوسط
            env_item = QTableWidgetItem(str(institution[3]))
            if institution[3] == "حضري":
                env_item.setForeground(QColor("#3498db"))
            else:
                env_item.setForeground(QColor("#27ae60"))
            self.institutions_table.setItem(row, 3, env_item)
            
            # السلك
            level_item = QTableWidgetItem(str(institution[4]))
            if institution[4] == "ابتدائي":
                level_item.setForeground(QColor("#27ae60"))
            elif institution[4] == "إعدادي":
                level_item.setForeground(QColor("#f39c12"))
            else:  # تأهيلي
                level_item.setForeground(QColor("#9b59b6"))
            self.institutions_table.setItem(row, 4, level_item)
            
            # النوع
            type_item = QTableWidgetItem(str(institution[5] or ""))
            if institution[5] == "خصوصية":
                type_item.setForeground(QColor("#e74c3c"))
            else:
                type_item.setForeground(QColor("#2c3e50"))
            self.institutions_table.setItem(row, 5, type_item)
            
            # المدير
            director_item = QTableWidgetItem(str(institution[6] or ""))
            director_item.setForeground(QColor("#2c3e50"))
            self.institutions_table.setItem(row, 6, director_item)
            
            # الهاتف
            phone_item = QTableWidgetItem(str(institution[7] or ""))
            phone_item.setForeground(QColor("#2c3e50"))
            self.institutions_table.setItem(row, 7, phone_item)
            
            # أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(5, 5, 5, 5)
            actions_layout.setSpacing(5)
            
            # زر التعديل
            edit_btn = QPushButton("تعديل")
            edit_btn.setFixedSize(60, 25)
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
            """)
            edit_btn.clicked.connect(lambda checked, r=row: self.edit_institution(r))
            
            # زر الحذف
            delete_btn = QPushButton("حذف")
            delete_btn.setFixedSize(60, 25)
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_institution(r))
            
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            
            self.institutions_table.setCellWidget(row, 8, actions_widget)
            
        # توسيط النص في الخلايا
        for row in range(self.institutions_table.rowCount()):
            for col in range(self.institutions_table.columnCount() - 1):  # ماعدا عمود الإجراءات
                item = self.institutions_table.item(row, col)
                if item:
                    item.setTextAlignment(Qt.AlignCenter)
                    
        # تحديث الإحصائيات
        self.stats_label.setText(f"إجمالي المؤسسات: {len(institutions)}")
        
    def add_institution(self):
        """إضافة مؤسسة جديدة"""
        if not self.academic_year_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار السنة الدراسية أولاً")
            return
            
        dialog = AddInstitutionDialog(self.db_manager, self.academic_year_id, self)
        if dialog.exec() == QDialog.Accepted:
            self.load_institutions()  # إعادة تحميل البيانات
            
    def edit_institution(self, row):
        """تعديل المؤسسة"""
        QMessageBox.information(self, "قريباً", "ستتوفر هذه الميزة قريباً")
        
    def delete_institution(self, row):
        """حذف المؤسسة"""
        institution_code = self.institutions_table.item(row, 0).text()
        institution_name = self.institutions_table.item(row, 1).text()
        
        # التحقق من وجود حوادث مرتبطة بالمؤسسة
        incidents_count = self.db_manager.execute_query("""
            SELECT COUNT(*) FROM incidents i
            JOIN educational_institutions ei ON i.institution_id = ei.id
            WHERE ei.institution_code = ? AND ei.academic_year_id = ?
        """, (institution_code, self.academic_year_id), fetch=True)
        
        if incidents_count and incidents_count[0][0] > 0:
            QMessageBox.warning(self, "تحذير", 
                              f"لا يمكن حذف المؤسسة لأنها مرتبطة بـ {incidents_count[0][0]} حادثة.\n"
                              f"يجب حذف الحوادث المرتبطة أولاً.")
            return
        
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   f"هل أنت متأكد من حذف المؤسسة؟\n"
                                   f"الرمز: {institution_code}\n"
                                   f"الاسم: {institution_name}\n\n"
                                   f"تحذير: لا يمكن التراجع عن هذا الإجراء!",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_query("""
                    DELETE FROM educational_institutions 
                    WHERE institution_code = ? AND academic_year_id = ?
                """, (institution_code, self.academic_year_id))
                
                QMessageBox.information(self, "تم الحذف", "تم حذف المؤسسة بنجاح")
                self.load_institutions()  # إعادة تحميل البيانات
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المؤسسة:\n{str(e)}")
        
    def reset_filters(self):
        """إعادة تعيين التصفية"""
        self.search_edit.clear()
        self.level_filter.setCurrentIndex(0)
        self.community_filter.setCurrentIndex(0)
        self.environment_filter.setCurrentIndex(0)
        self.type_filter.setCurrentIndex(0)
        self.filter_institutions()
