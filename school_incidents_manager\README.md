# منصة تدبير الحوادث المدرسية

## نظرة عامة
تطبيق سطح مكتب حديث لإدارة الحوادث المدرسية باللغة العربية، مطور باستخدام Python و PySide6.

## البيانات الافتراضية المؤقتة

تم إدراج بيانات افتراضية مؤقتة لأغراض الاختبار والعرض التوضيحي، وتشمل:

### السنوات الدراسية (8 سنوات)
- 2018-2019
- 2019-2020  
- 2020-2021
- 2021-2022
- 2022-2023
- 2023-2024
- 2024-2025
- 2025-2026

### المؤسسات التعليمية (72 مؤسسة)
تم إنشاء 9 مؤسسات نموذجية لكل سنة دراسية:

#### المؤسسات الابتدائية (3 مؤسسات):
- **مدرسة الأمل الابتدائية** (PRIM001)
  - الجماعة: الرباط
  - الوسط: حضري
  - المدير: أحمد المدير
  - الهاتف: 0661234567

- **مدرسة النور الابتدائية** (PRIM002)
  - الجماعة: سلا
  - الوسط: حضري
  - المديرة: فاطمة المديرة
  - الهاتف: 0662345678

- **مدرسة القرية الابتدائية** (PRIM003)
  - الجماعة: تمارة
  - الوسط: قروي
  - المدير: محمد الإدريسي
  - الهاتف: 0663456789

#### المؤسسات الإعدادية (3 مؤسسات):
- **إعدادية الاستقلال** (COLL001)
  - الجماعة: الرباط
  - الوسط: حضري
  - المديرة: زينب العلوي
  - الهاتف: 0664567890

- **إعدادية المستقبل** (COLL002)
  - الجماعة: سلا
  - الوسط: حضري
  - النوع: خصوصية
  - المدير: عبد الرحمن حسني
  - الهاتف: 0665678901

- **إعدادية الوحدة** (COLL003)
  - الجماعة: الخميسات
  - الوسط: قروي
  - المديرة: نادية الشريف
  - الهاتف: 0666789012

#### المؤسسات التأهيلية (3 مؤسسات):
- **ثانوية محمد الخامس التأهيلية** (LYC001)
  - الجماعة: الرباط
  - الوسط: حضري
  - المدير: كريم البركاني
  - الهاتف: 0667890123

- **ثانوية الحسن الثاني التأهيلية** (LYC002)
  - الجماعة: الدار البيضاء
  - الوسط: حضري
  - المديرة: سعاد المغربي
  - الهاتف: 0668901234

- **ثانوية الأندلس التأهيلية** (LYC003)
  - الجماعة: فاس
  - الوسط: حضري
  - النوع: خصوصية
  - المدير: عبد الله الفاسي
  - الهاتف: 0669012345

### الحوادث المدرسية (40 حادثة)
تم إنشاء 5 حوادث نموذجية لكل سنة دراسية:

1. **أحمد محمد علي**
   - النوع: ذكر
   - رقم المسار: 12345
   - المرجع: REF001
   - تاريخ الحادثة: 2024-03-15
   - تاريخ التسوية: 2024-04-15
   - الحالة: تمت التسوية
   - نوع الحادثة: حادثة مدرسية
   - الخطورة: عادية

2. **فاطمة الزهراء أحمد**
   - النوع: أنثى
   - رقم المسار: 23456
   - المرجع: REF002
   - تاريخ الحادثة: 2024-02-20
   - تاريخ التسوية: 2024-03-20
   - الحالة: في طور التسوية
   - نوع الحادثة: حادثة تنقل
   - الخطورة: خطيرة

3. **محمد عبد الله حسن**
   - النوع: ذكر
   - رقم المسار: 34567
   - المرجع: REF003
   - تاريخ الحادثة: 2024-01-10
   - تاريخ التسوية: 2024-02-10
   - الحالة: تم الدفع
   - نوع الحادثة: حادثة رياضية
   - الخطورة: عادية

4. **عائشة سعد الدين**
   - النوع: أنثى
   - رقم المسار: 45678
   - المرجع: REF004
   - تاريخ الحادثة: 2024-04-05
   - تاريخ التسوية: 2024-05-05
   - الحالة: مرفوض
   - نوع الحادثة: حادثة مدرسية
   - الخطورة: عادية

5. **يوسف عبد الرحمن**
   - النوع: ذكر
   - رقم المسار: 56789
   - المرجع: REF005
   - تاريخ الحادثة: 2024-03-25
   - تاريخ التسوية: 2024-04-25
   - الحالة: تمت التسوية
   - نوع الحادثة: حادثة تنقل
   - الخطورة: خطيرة

### المستفيدون من التعويض (40 مستفيد)
تم إنشاء مستفيد واحد (الأب) لكل حادثة مع:
- الاسم: والد [اسم التلميذ]
- الهاتف: 066X234567

## تشغيل التطبيق

```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# اختبار شامل للتطبيق (موصى به)
python test_app.py

# تشغيل التطبيق (الطريقة الموصى بها - بدون رسائل تحذيرية)
python run_app.py

# تشغيل التطبيق (الطريقة العادية)
python main.py

# اختبار قاعدة البيانات فقط
python test_db.py
```

## ملاحظات مهمة

⚠️ **هذه بيانات افتراضية مؤقتة لأغراض الاختبار فقط**

- البيانات ليست حقيقية وتم إنشاؤها للعرض التوضيحي
- يمكن حذف هذه البيانات واستبدالها ببيانات حقيقية
- تحتوي قاعدة البيانات على نظام مرن لإدارة السنوات الدراسية
- كل مؤسسة لها رمز فريد يضمن عدم التأثير على البيانات التاريخية

## المشاكل المحلولة

✅ **تم إصلاح المشاكل التالية:**

- **مشكلة رسائل CSS التحذيرية**: تم إنشاء `run_app.py` لتشغيل التطبيق بدون رسائل تحذيرية
- **تكرار الدوال**: تم إزالة التكرار في `dashboard_page.py`
- **مشكلة استدعاء دالة غير موجودة**: تم إصلاح `update_dashboard` إلى `refresh_data`
- **تنظيف الكود**: تم إزالة التعليقات غير المستخدمة
- **تحسين المتطلبات**: تم إزالة مكتبة `qfluentwidgets` غير الضرورية
- **اختبار شامل**: تم إضافة `test_app.py` لاختبار جميع مكونات التطبيق

## ملفات جديدة تم إضافتها

📁 **الملفات المضافة:**
- `run_app.py`: مشغل التطبيق بدون رسائل تحذيرية
- `test_app.py`: اختبار شامل لجميع مكونات التطبيق
- `src/utils/qt_fixes.py`: إصلاحات وتحسينات Qt

## المميزات المتوفرة

✅ **مُنجز:**
- هيكل قاعدة البيانات الكامل
- البيانات الافتراضية للاختبار
- النافذة الرئيسية مع الشريط الجانبي
- صفحة لوحة المعلومات الأساسية
- دعم اللغة العربية والاتجاه من اليمين إلى اليسار
- تصميم حديث ومتجاوب

🔄 **قيد التطوير:**
- صفحة إضافة الحوادث
- صفحة قائمة الحوادث مع التصفية والبحث
- صفحة إدارة المؤسسات التعليمية
- صفحة التقارير والإحصائيات
- صفحة الإعدادات
- نظام إدارة المستخدمين
- استيراد البيانات من ملفات Excel

## البنية التقنية

- **لغة البرمجة:** Python 3.13
- **واجهة المستخدم:** PySide6 (Qt for Python)
- **قاعدة البيانات:** SQLite3
- **الهندسة:** Modular Architecture
- **الدعم:** Windows 7+, Arabic RTL
