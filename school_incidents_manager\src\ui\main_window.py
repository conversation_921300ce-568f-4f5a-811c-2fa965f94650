from PySide6.QtWidgets import (Q<PERSON>ain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QStackedWidget, QPushButton, QLabel, QComboBox,
                            QFrame, QSizePolicy, QApplication)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QPixmap, QFont
import sys
import os

# إضافة مجلد src إلى المسار
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir))
sys.path.append(src_dir)

from database.database_manager import DatabaseManager
from utils.ui_utils import StyleSheet, setup_rtl_support
from ui.dashboard_page import DashboardPage
from ui.add_incident_page import AddIncidentPage
from ui.incidents_list_page import IncidentsListPage
from ui.institutions_page import InstitutionsPage
from ui.reports_page import ReportsPage
from ui.settings_page import SettingsPage

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_academic_year_id = None
        self.setup_ui()
        self.load_academic_years()
        self.apply_styles()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الرئيسية"""
        self.setWindowTitle("منصة إدارة الحوادث المدرسية")
        self.setMinimumSize(1200, 800)
        self.setWindowState(Qt.WindowMaximized)
        
        # إنشاء الويدجت الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # تخطيط رئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء الشريط العلوي
        self.create_header()
        main_layout.addWidget(self.header_widget)
        
        # إنشاء المحتوى الرئيسي
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # إنشاء القائمة الجانبية
        self.create_sidebar()
        content_layout.addWidget(self.sidebar)
        
        # إنشاء منطقة المحتوى
        self.create_content_area()
        content_layout.addWidget(self.content_area)
        
        content_widget = QWidget()
        content_widget.setLayout(content_layout)
        main_layout.addWidget(content_widget)
        
    def create_header(self):
        """إنشاء الشريط العلوي"""
        self.header_widget = QFrame()
        self.header_widget.setObjectName("header_widget")
        self.header_widget.setFixedHeight(80)
        
        header_layout = QHBoxLayout(self.header_widget)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # شعار التطبيق
        logo_label = QLabel("🏫")
        logo_label.setObjectName("logo_label")
        logo_label.setFixedSize(50, 50)
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("font-size: 24pt;")
        header_layout.addWidget(logo_label)
        
        # عنوان التطبيق
        title_label = QLabel("منصة إدارة الحوادث المدرسية")
        title_label.setObjectName("header_title")
        header_layout.addWidget(title_label)
        
        # مساحة فارغة للتمدد
        header_layout.addStretch()
        
        # قائمة السنوات الدراسية
        year_label = QLabel("السنة الدراسية:")
        year_label.setStyleSheet("color: #2c3e50; font-size: 12pt; font-weight: bold;")
        header_layout.addWidget(year_label)
        
        self.academic_year_combo = QComboBox()
        self.academic_year_combo.setFixedWidth(150)
        self.academic_year_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                color: #2E86AB;
                font-weight: bold;
                padding: 8px;
                border-radius: 6px;
            }
        """)
        self.academic_year_combo.currentTextChanged.connect(self.on_academic_year_changed)
        header_layout.addWidget(self.academic_year_combo)
        
    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        self.sidebar = QFrame()
        self.sidebar.setObjectName("sidebar")
        self.sidebar.setFixedWidth(250)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(5)
        
        # أزرار القائمة
        menu_items = [
            ("الرئيسية", "🏠", "dashboard"),
            ("إضافة حادثة", "➕", "add_incident"),
            ("قائمة الحوادث", "📋", "incidents_list"),
            ("المؤسسات التعليمية", "🏫", "institutions"),
            ("التقارير", "📊", "reports"),
            ("الإعدادات", "⚙️", "settings")
        ]
        
        self.menu_buttons = {}
        
        for text, icon, page_name in menu_items:
            btn = QPushButton(f"{icon} {text}")
            btn.setObjectName("menu_button")
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, name=page_name: self.show_page(name))
            sidebar_layout.addWidget(btn)
            self.menu_buttons[page_name] = btn
        
        # تفعيل الصفحة الرئيسية بشكل افتراضي
        self.menu_buttons["dashboard"].setChecked(True)
        
        # مساحة فارغة في الأسفل
        sidebar_layout.addStretch()
        
        # معلومات المطور
        dev_label = QLabel("تطوير: فريق التطوير\nالإصدار: 1.0")
        dev_label.setStyleSheet("color: #6c757d; font-size: 9pt; text-align: center;")
        dev_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(dev_label)
        
    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        self.content_area = QStackedWidget()
        
        # إنشاء الصفحات
        self.pages = {
            "dashboard": DashboardPage(self.db_manager),
            "add_incident": AddIncidentPage(self.db_manager),
            "incidents_list": IncidentsListPage(self.db_manager),
            "institutions": InstitutionsPage(self.db_manager),
            "reports": ReportsPage(self.db_manager),
            "settings": SettingsPage(self.db_manager)
        }
        
        # إضافة الصفحات إلى المكدس
        for page in self.pages.values():
            self.content_area.addWidget(page)
        
        # عرض الصفحة الرئيسية بشكل افتراضي
        self.content_area.setCurrentWidget(self.pages["dashboard"])
        
    def load_academic_years(self):
        """تحميل السنوات الدراسية"""
        years = self.db_manager.get_academic_years()
        self.academic_year_combo.clear()
        
        for year_id, year_name in years:
            self.academic_year_combo.addItem(year_name, year_id)
        
        # تعيين السنة الحالية كافتراضية
        current_year = self.db_manager.get_setting("current_academic_year")
        if current_year:
            index = self.academic_year_combo.findText(current_year)
            if index >= 0:
                self.academic_year_combo.setCurrentIndex(index)
        
        # تعيين السنة الحالية
        if self.academic_year_combo.count() > 0:
            self.current_academic_year_id = self.academic_year_combo.currentData()
            self.update_pages_academic_year()
            
    def on_academic_year_changed(self):
        """عند تغيير السنة الدراسية"""
        self.current_academic_year_id = self.academic_year_combo.currentData()
        self.update_pages_academic_year()
        
        # حفظ السنة المختارة في الإعدادات
        current_year = self.academic_year_combo.currentText()
        self.db_manager.update_setting("current_academic_year", current_year)
        
    def update_pages_academic_year(self):
        """تحديث السنة الدراسية في جميع الصفحات"""
        for page in self.pages.values():
            if hasattr(page, 'set_academic_year'):
                page.set_academic_year(self.current_academic_year_id)
                
    def show_page(self, page_name):
        """عرض صفحة معينة"""
        # إلغاء تحديد جميع الأزرار
        for btn in self.menu_buttons.values():
            btn.setChecked(False)
        
        # تحديد الزر المطلوب
        self.menu_buttons[page_name].setChecked(True)
        
        # عرض الصفحة
        if page_name in self.pages:
            self.content_area.setCurrentWidget(self.pages[page_name])
            
            # تحديث البيانات إذا كانت الصفحة تدعم ذلك
            if hasattr(self.pages[page_name], 'refresh_data'):
                self.pages[page_name].refresh_data()
                
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet(StyleSheet.get_main_style() + StyleSheet.get_card_style())
        
    def closeEvent(self, event):
        """عند إغلاق التطبيق"""
        event.accept()

def main():
    app = QApplication(sys.argv)
    
    # إعداد دعم اللغة العربية
    setup_rtl_support()
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
