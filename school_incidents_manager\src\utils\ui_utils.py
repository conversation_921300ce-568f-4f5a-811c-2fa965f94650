import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QTranslator, QLocale
from PySide6.QtGui import QFont

def setup_rtl_support():
    """إعداد دعم اللغة العربية والاتجاه من اليمين إلى اليسار"""
    app = QApplication.instance()
    if app:
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين الخط العربي
        arabic_font = QFont("Segoe UI", 10)
        arabic_font.setStyleHint(QFont.System)
        app.setFont(arabic_font)

def show_message(parent, title, message, msg_type="info"):
    """عرض رسالة للمستخدم"""
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    if msg_type == "info":
        msg_box.setIcon(QMessageBox.Information)
    elif msg_type == "warning":
        msg_box.setIcon(QMessageBox.Warning)
    elif msg_type == "error":
        msg_box.setIcon(QMessageBox.Critical)
    elif msg_type == "question":
        msg_box.setIcon(QMessageBox.Question)
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        return msg_box.exec_() == QMessageBox.Yes
    
    msg_box.exec_()
    return True

def format_date(date_obj):
    """تنسيق التاريخ"""
    if date_obj:
        return date_obj.strftime("%Y-%m-%d")
    return ""

def validate_required_fields(fields_dict):
    """التحقق من الحقول المطلوبة"""
    missing_fields = []
    for field_name, field_value in fields_dict.items():
        if not field_value or (isinstance(field_value, str) and field_value.strip() == ""):
            missing_fields.append(field_name)
    
    return missing_fields

def get_resource_path(relative_path):
    """الحصول على مسار الموارد"""
    try:
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    
    return os.path.join(base_path, relative_path)

class StyleSheet:
    """أنماط CSS للتطبيق"""
    
    @staticmethod
    def get_main_style():
        return """
        QMainWindow {
            background-color: #f8f9fa;
            color: #212529;
        }

        QWidget {
            font-family: 'Segoe UI', 'Tahoma', Arial, sans-serif;
            font-size: 11pt;
            color: #212529;
        }

        /* شريط علوي */
        #header_widget {
            background-color: #ffffff;
            border: none;
            padding: 10px;
            border-bottom: 2px solid #2E86AB;
        }

        #header_title {
            color: #2E86AB;
            font-size: 18pt;
            font-weight: bold;
            text-align: right;
            qproperty-alignment: AlignRight;
        }

        #logo_label {
            color: #2E86AB;
            font-size: 14pt;
        }
        
        /* قائمة جانبية */
        #sidebar {
            background-color: #ffffff;
            border-right: 1px solid #dee2e6;
            max-width: 250px;
            min-width: 200px;
        }

        #sidebar QPushButton {
            text-align: right;
            padding: 12px 20px;
            border: none;
            background-color: transparent;
            color: #495057;
            font-size: 11pt;
            margin: 2px 5px;
            border-radius: 8px;
            qproperty-alignment: AlignRight;
        }

        #sidebar QPushButton:hover {
            background-color: #e9ecef;
            color: #2E86AB;
        }

        #sidebar QPushButton:checked {
            background-color: #2E86AB;
            color: white;
        }
        
        /* أزرار */
        QPushButton {
            background-color: #2E86AB;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            text-align: center;
            qproperty-alignment: AlignCenter;
        }

        QPushButton:hover {
            background-color: #1e5f82;
        }

        QPushButton:pressed {
            background-color: #164c6b;
        }

        QPushButton:disabled {
            background-color: #6c757d;
            color: #adb5bd;
        }
        
        /* أزرار ثانوية */
        .secondary-button {
            background-color: #6c757d;
        }
        
        .secondary-button:hover {
            background-color: #5a6268;
        }
        
        .success-button {
            background-color: #43AA8B;
        }
        
        .success-button:hover {
            background-color: #369970;
        }
        
        .warning-button {
            background-color: #F9844A;
        }
        
        .warning-button:hover {
            background-color: #f7733a;
        }
        
        .danger-button {
            background-color: #F8333C;
        }
        
        .danger-button:hover {
            background-color: #f01e28;
        }
        
        /* حقول الإدخال */
        QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
            color: #212529;
            selection-background-color: #2E86AB;
            selection-color: white;
            text-align: right;
            qproperty-alignment: AlignRight;
        }

        QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
            border-color: #2E86AB;
            outline: none;
            background-color: white;
        }
        
        /* جداول */
        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #cce5f4;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            color: #212529;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #dee2e6;
            color: #212529;
            text-align: right;
        }

        QTableWidget::item:selected {
            background-color: #2E86AB;
            color: white;
        }

        QHeaderView::section {
            background-color: #e9ecef;
            color: #495057;
            padding: 10px;
            border: none;
            font-weight: bold;
            text-align: right;
        }
        
        /* مربعات الاختيار */
        QCheckBox {
            spacing: 8px;
            color: #495057;
            text-align: right;
        }

        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 3px;
            background-color: white;
        }

        QCheckBox::indicator:checked {
            background-color: #2E86AB;
            border-color: #2E86AB;
        }
        
        /* أزرار الراديو */
        QRadioButton {
            spacing: 8px;
            color: #495057;
            text-align: right;
        }

        QRadioButton::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 9px;
            background-color: white;
        }

        QRadioButton::indicator:checked {
            background-color: #2E86AB;
            border-color: #2E86AB;
        }
        
        /* شريط التمرير */
        QScrollBar:vertical {
            border: none;
            background: #f8f9fa;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background: #ced4da;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background: #adb5bd;
        }
        
        /* تبويبات */
        QTabWidget::pane {
            border: 1px solid #dee2e6;
            background-color: white;
            border-radius: 6px;
        }
        
        QTabBar::tab {
            background-color: #e9ecef;
            color: #495057;
            padding: 10px 20px;
            margin-left: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        
        QTabBar::tab:selected {
            background-color: #2E86AB;
            color: white;
        }
        
        QTabBar::tab:hover:!selected {
            background-color: #dee2e6;
        }
        
        /* مجموعات */
        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 20px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2E86AB;
        }
        
        /* شريط الحالة */
        QStatusBar {
            background-color: #e9ecef;
            color: #495057;
            border-top: 1px solid #dee2e6;
        }
        
        /* أشرطة التقدم */
        QProgressBar {
            border: 2px solid #dee2e6;
            border-radius: 6px;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        QProgressBar::chunk {
            background-color: #2E86AB;
            border-radius: 4px;
        }
        """
    
    @staticmethod
    def get_card_style():
        return """
        .card {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 10px;
        }
        
        .card-header {
            font-size: 14pt;
            font-weight: bold;
            color: #2E86AB;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .stats-card {
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1, 
                                      stop: 0 #2E86AB, stop: 1 #1e5f82);
            color: white;
            border: none;
        }
        
        .stats-number {
            font-size: 24pt;
            font-weight: bold;
        }
        
        .stats-label {
            font-size: 11pt;
            opacity: 0.9;
        }
        """
